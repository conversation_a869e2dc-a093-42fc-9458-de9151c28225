<?php

namespace App\Http\Controllers;

use App\Models\BusinessSetting;
use App\Models\PaymentMethod;
use App\Repositories\CoreComponentRepository;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Laracasts\Flash\Flash;


class BusinessSettingController extends Controller
{
    public function __construct()
    {
    }

    public function general_setting(Request $request)
    {
        return view('backend.setup_configurations.general_settings');
    }

    public function activation(Request $request)
    {
        // Retrieve tailwind colors from database
        $tailwind_config = BusinessSetting::where('type', 'tailwind_colors')->first();

        $tailwind_colors = $tailwind_config
            ? json_decode($tailwind_config->value, true)
            : [
                'primary' => get_setting('primary', '#3490dc'),
                'secondary' => get_setting('secondary', '#ffed4a'),
                'accent' => get_setting('accent', '#f6993f'),
                'text' => get_setting('text', '#333333'),
                'background' => get_setting('background', '#ffffff'),
            ];

        return view('backend.setup_configurations.activation', compact('tailwind_colors'));
    }

    // Helper method to adjust color brightness
    private function adjustColorBrightness($hex, $steps)
    {
        // Remove # if present
        $hex = ltrim($hex, '#');

        // Convert to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));

        // Adjust brightness
        $r = max(0, min(255, $r + $steps));
        $g = max(0, min(255, $g + $steps));
        $b = max(0, min(255, $b + $steps));

        // Convert back to hex
        return sprintf("#%02x%02x%02x", $r, $g, $b);
    }

    public function generateDynamicCss()
    {
        // Retrieve colors from database
        $tailwind_config = BusinessSetting::where('type', 'tailwind_colors')->first();
        $colors = $tailwind_config ? json_decode($tailwind_config->value, true) : [];

        // Set default colors if not found
        $primary = $colors['primary'] ?? '#39B7B4';
        $secondary = $colors['secondary'] ?? '#002333';
        $accent = $colors['accent'] ?? '#f6993f';
        $text = $colors['text'] ?? '#333333';
        $background = $colors['background'] ?? '#ffffff';

        // Calculate hover and disabled colors (slightly darker/lighter variants)
        $primary_hover = $this->adjustColorBrightness($primary, -10);
        $primary_disabled = $this->adjustColorBrightness($primary, 30);
        $text_hover = $this->adjustColorBrightness($text, -20);
        $text_light = $this->adjustColorBrightness($text, 80);
        $background_darker = $this->adjustColorBrightness($background, -5);

        // Generate CSS content - simplified to just CSS variables
        $css = ":root {\n";
        $css .= "  --primary-color: {$primary};\n";
        $css .= "  --primary-color-hover: {$primary_hover};\n";
        $css .= "  --primary-color-disabled: {$primary_disabled};\n";
        $css .= "  --secondary-color: {$secondary};\n";
        $css .= "  --accent-color: {$accent};\n";
        $css .= "  --text-color: {$text};\n";
        $css .= "  --text-color-hover: {$text_hover};\n";
        $css .= "  --text-color-light: {$text_light};\n";
        $css .= "  --background-color: {$background};\n";
        $css .= "  --background-color-darker: {$background_darker};\n";
        $css .= "}\n\n";

        // No need for utility classes here as they'll be generated by Tailwind

        return response($css)
            ->header('Content-Type', 'text/css')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }

    /**
     * Update the Tailwind configuration CSS file
     * @param array $colors
     * @return bool
     */
    public function updateTailwindConfig($colors)
    {
        try {
            // Don't modify the tailwind.config.js file directly
            // Use CSS variables instead that Tailwind can reference

            // Define full path for assets directory
            $cssVarsDir = public_path('assets/front/css');
            $cssVarsPath = $cssVarsDir . '/theme-vars.css';

            // Make sure the directory exists with proper permissions
            if (!file_exists($cssVarsDir) && env('DEMO_MODE') != 'On') {
                if (!mkdir($cssVarsDir, 0755, true)) {
                    \Log::error("Failed to create directory: {$cssVarsDir}");
                    return false;
                }
            }

            // Verify directory is writable
            if (!is_writable($cssVarsDir) && env('DEMO_MODE') != 'On') {
                \Log::error("Directory not writable: {$cssVarsDir}");
                return false;
            }

            // Validate colors to ensure they're all proper hex codes
            foreach ($colors as $key => $value) {
                if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $value)) {
                    \Log::warning("Invalid color format for {$key}: {$value}");
                    // Use a default color instead of failing
                    $colors[$key] = match($key) {
                        'primary' => '#3490dc',
                        'secondary' => '#ffed4a',
                        'accent' => '#f6993f',
                        'text' => '#333333',
                        'background' => '#ffffff',
                        default => '#000000'
                    };
                }
            }

            // Calculate derived colors with error handling
            try {
                $primary_hover = $this->adjustColorBrightness($colors['primary'], -10);
                $primary_disabled = $this->adjustColorBrightness($colors['primary'], 30);
                $text_hover = $this->adjustColorBrightness($colors['text'], -20);
                $text_light = $this->adjustColorBrightness($colors['text'], 80);
                $background_darker = $this->adjustColorBrightness($colors['background'], -5);
            } catch (\Exception $e) {
                \Log::error("Error calculating color variants: " . $e->getMessage());

                // Fallback values if color adjustment fails
                $primary_hover = $colors['primary'];
                $primary_disabled = $colors['primary'];
                $text_hover = $colors['text'];
                $text_light = $colors['text'];
                $background_darker = $colors['background'];
            }

            // Generate CSS variables
            $css = ":root {\n";
            $css .= "  --primary-color: {$colors['primary']};\n";
            $css .= "  --primary-color-hover: {$primary_hover};\n";
            $css .= "  --primary-color-disabled: {$primary_disabled};\n";
            $css .= "  --secondary-color: {$colors['secondary']};\n";
            $css .= "  --accent-color: {$colors['accent']};\n";
            $css .= "  --text-color: {$colors['text']};\n";
            $css .= "  --text-color-hover: {$text_hover};\n";
            $css .= "  --text-color-light: {$text_light};\n";
            $css .= "  --background-color: {$colors['background']};\n";
            $css .= "  --background-color-darker: {$background_darker};\n";
            $css .= "}\n";

            // Write to file if not in demo mode with error handling
            if (env('DEMO_MODE') != 'On') {
                if (file_put_contents($cssVarsPath, $css) === false) {
                    \Log::error("Failed to write CSS file: {$cssVarsPath}");
                    return false;
                }

                // Log success
                \Log::info("Tailwind CSS variables successfully written to {$cssVarsPath}");
            } else {
                \Log::info("Demo mode active - CSS file not written");
            }

            return true;
        } catch (\Exception $e) {
            \Log::error("Exception in updateTailwindConfig: " . $e->getMessage());
            return false;
        }
    }

    public function social_login(Request $request)
    {
        return view('backend.setup_configurations.social_login');
    }

    public function social_login_update(Request $request)
    {
        if ($request->type == 'social_login') {
            // Update Google Login settings
            $this->overWriteEnvFile('GOOGLE_CLIENT_ID', $request->GOOGLE_CLIENT_ID);
            $this->overWriteEnvFile('GOOGLE_CLIENT_SECRET', $request->GOOGLE_CLIENT_SECRET);
            $this->overWriteEnvFile('GOOGLE_REDIRECT_URI', $request->GOOGLE_REDIRECT_URI);

            // Update the database setting
            $this->updateSetting('google_login', $request->has('google_login') ? 1 : 0);

            flash(translate('Social Login settings updated successfully'))->success();
            return back();
        }

        return back();
    }

    protected function updateSetting($key, $value)
    {
        $setting = BusinessSetting::where('type', $key)->first();

        if (!$setting) {
            $setting = new BusinessSetting();
            $setting->type = $key;
        }

        $setting->value = $value;
        $setting->save();

        return $setting;
    }

    public function smtp_settings(Request $request)
    {
        return view('backend.setup_configurations.smtp_settings');
    }

    public function google_analytics(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();
        CoreComponentRepository::initializeCache();
        return view('backend.setup_configurations.google_configuration.google_analytics');
    }

    public function google_recaptcha(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();
        CoreComponentRepository::initializeCache();
        return view('backend.setup_configurations.google_configuration.google_recaptcha');
    }

    public function google_map(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();
        CoreComponentRepository::initializeCache();
        return view('backend.setup_configurations.google_configuration.google_map');
    }

    public function google_firebase(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();
        CoreComponentRepository::initializeCache();
        return view('backend.setup_configurations.google_configuration.google_firebase');
    }

    public function facebook_chat(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();
        CoreComponentRepository::initializeCache();
        return view('backend.setup_configurations.facebook_chat');
    }

    public function facebook_comment(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();
        CoreComponentRepository::initializeCache();
        return view('backend.setup_configurations.facebook_configuration.facebook_comment');
    }

    public function payment_method(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();
        CoreComponentRepository::initializeCache();
        $payment_methods = PaymentMethod::whereNull('addon_identifier')->get();
        return view('backend.setup_configurations.payment_method.index', compact('payment_methods'));
    }

    public function file_system(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();
        CoreComponentRepository::initializeCache();
        return view('backend.setup_configurations.file_system');
    }

    /**
     * Update the API key's for payment methods.
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function payment_method_update(Request $request)
    {
        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', $request->payment_method . '_sandbox')->first();
        if ($business_settings != null) {
            if ($request->has($request->payment_method . '_sandbox')) {
                $business_settings->value = 1;
                $business_settings->save();
            } else {
                $business_settings->value = 0;
                $business_settings->save();
            }
        }

        Artisan::call('cache:clear');

        flash(translate("Settings updated successfully"))->success();
        return back();
    }

    /**
     * Update the API key's for GOOGLE analytics.
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function google_analytics_update(Request $request)
    {
        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', 'google_analytics')->first();

        if ($request->has('google_analytics')) {
            $business_settings->value = 1;
            $business_settings->save();
        } else {
            $business_settings->value = 0;
            $business_settings->save();
        }

        Artisan::call('cache:clear');

        flash(translate("Settings updated successfully"))->success();
        return back();
    }

    public function google_recaptcha_update(Request $request)
    {
        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', 'google_recaptcha')->first();

        if ($request->has('google_recaptcha')) {
            $business_settings->value = 1;
            $business_settings->save();
        } else {
            $business_settings->value = 0;
            $business_settings->save();
        }

        Artisan::call('cache:clear');

        flash(translate("Settings updated successfully"))->success();
        return back();
    }

    public function google_map_update(Request $request)
    {
        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', 'google_map')->first();

        if ($request->has('google_map')) {
            $business_settings->value = 1;
            $business_settings->save();
        } else {
            $business_settings->value = 0;
            $business_settings->save();
        }

        Artisan::call('cache:clear');

        flash(translate("Settings updated successfully"))->success();
        return back();
    }

    public function google_firebase_update(Request $request)
    {
        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', 'google_firebase')->first();

        if ($request->has('google_firebase')) {
            $business_settings->value = 1;
            $business_settings->save();
        } else {
            $business_settings->value = 0;
            $business_settings->save();
        }

        Artisan::call('cache:clear');

        flash(translate("Settings updated successfully"))->success();
        return back();
    }


    /**
     * Update the API key's for GOOGLE analytics.
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function facebook_chat_update(Request $request)
    {
        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', 'facebook_chat')->first();

        if ($request->has('facebook_chat')) {
            $business_settings->value = 1;
            $business_settings->save();
        } else {
            $business_settings->value = 0;
            $business_settings->save();
        }

        Artisan::call('cache:clear');

        flash(translate("Settings updated successfully"))->success();
        return back();
    }

    public function facebook_comment_update(Request $request)
    {
        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', 'facebook_comment')->first();
        if (!$business_settings) {
            $business_settings = new BusinessSetting;
            $business_settings->type = 'facebook_comment';
        }

        $business_settings->value = 0;
        if ($request->facebook_comment) {
            $business_settings->value = 1;
        }

        $business_settings->save();

        Artisan::call('cache:clear');

        flash(translate("Settings updated successfully"))->success();
        return back();
    }

    public function facebook_pixel_update(Request $request)
    {
        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', 'facebook_pixel')->first();

        if ($request->has('facebook_pixel')) {
            $business_settings->value = 1;
            $business_settings->save();
        } else {
            $business_settings->value = 0;
            $business_settings->save();
        }

        Artisan::call('cache:clear');

        flash(translate("Settings updated successfully"))->success();
        return back();
    }

    /**
     * Update the API key's for other methods.
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function env_key_update(Request $request)
    {
        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        flash(translate("Settings updated successfully"))->success();
        return back();
    }

    /**
     * Safely update the .env file with new values
     * @param  String $key   The environment variable key
     * @param  String $value The environment variable value
     * @return bool
     */
    public function overWriteEnvFile($key, $value)
    {
        if (env('DEMO_MODE') == 'On' || app()->environment('production')) {
            return false;
        }

        // Validate the key to prevent injection
        if (!preg_match('/^[A-Z][A-Z0-9_]*$/', $key)) {
            \Log::error("Invalid environment key format: {$key}");
            return false;
        }

        try {
            // Use Laravel's built-in environment file writer
            $path = app()->environmentFilePath();

            if (!file_exists($path)) {
                \Log::error(".env file not found");
                return false;
            }

            // Escape the value properly
            $value = is_bool($value) ? ($value ? 'true' : 'false') : $value;
            $value = !in_array($value, ['true', 'false', 'null']) ? '"' . addslashes(trim($value)) . '"' : $value;

            // Get current content of .env file
            $content = file_get_contents($path);

            // Check if key exists
            $keyPosition = strpos($content, "{$key}=");

            if ($keyPosition !== false) {
                // Key exists, update its value
                $endOfLinePosition = strpos($content, "\n", $keyPosition);
                $oldLine = substr($content, $keyPosition, $endOfLinePosition !== false ? $endOfLinePosition - $keyPosition : strlen($content));
                $newLine = "{$key}={$value}";
                $content = str_replace($oldLine, $newLine, $content);
            } else {
                // Key doesn't exist, append it
                $content .= "\n{$key}={$value}\n";
            }

            // Write the updated content back to the .env file
            file_put_contents($path, $content);

            // Update the environment variable in the current process
            $_ENV[$key] = $value;
            putenv("{$key}={$value}");

            return true;
        } catch (\Exception $e) {
            \Log::error("Failed to update environment variable: " . $e->getMessage());
            return false;
        }
    }

    public function seller_verification_form(Request $request)
    {
        return view('backend.sellers.seller_verification_form.index');
    }

    /**
     * Update sell verification form.
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function seller_verification_form_update(Request $request)
    {
        $form = array();
        $select_types = ['select', 'multi_select', 'radio'];
        $j = 0;
        for ($i = 0; $i < count($request->type); $i++) {
            $item['type'] = $request->type[$i];
            $item['label'] = $request->label[$i];
            if (in_array($request->type[$i], $select_types)) {
                $item['options'] = json_encode($request['options_' . $request->option[$j]]);
                $j++;
            }
            array_push($form, $item);
        }
        $business_settings = BusinessSetting::where('type', 'verification_form')->first();
        $business_settings->value = json_encode($form);
        if ($business_settings->save()) {
            Artisan::call('cache:clear');

            flash(translate("Verification form updated successfully"))->success();
            return back();
        }
    }

    public function update(Request $request)
    {
        foreach ($request->types as $key => $type) {
            if ($type == 'site_name') {
                $this->overWriteEnvFile('APP_NAME', $request[$type]);
            }
            if ($type == 'timezone') {
                $this->overWriteEnvFile('APP_TIMEZONE', $request[$type]);
            } else {
                $lang = null;
                if (gettype($type) == 'array') {
                    $lang = array_key_first($type);
                    $type = $type[$lang];
                    $business_settings = BusinessSetting::where('type', $type)->where('lang', $lang)->first();
                } else {
                    $business_settings = BusinessSetting::where('type', $type)->first();
                }

                if ($business_settings != null) {
                    if (gettype($request[$type]) == 'array') {
                        $business_settings->value = json_encode($request[$type]);
                    } else {
                        $business_settings->value = $request[$type];
                    }
                    $business_settings->lang = $lang;
                    $business_settings->save();
                } else {
                    $business_settings = new BusinessSetting;
                    $business_settings->type = $type;
                    if (gettype($request[$type]) == 'array') {
                        $business_settings->value = json_encode($request[$type]);
                    } else {
                        $business_settings->value = $request[$type];
                    }
                    $business_settings->lang = $lang;
                    $business_settings->save();
                }
            }
        }

        Artisan::call('cache:clear');

        flash(translate("Settings updated successfully"))->success();
        // If the request from a tabs with tab input
        if ($request->has('tab')) {
            return Redirect::to(URL::previous() . "#" . $request->tab);
        }
        return redirect()->back();
    }

    public function updateActivationSettings(Request $request)
    {
        try {
            $env_changes = ['FORCE_HTTPS', 'FILESYSTEM_DRIVER'];

            // Directly access form input instead of using validation
            $type = $request->input('type');
            $value = $request->input('value');

            if (in_array($type, $env_changes)) {
                return $this->updateActivationSettingsInEnv($request);
            }

            $business_settings = BusinessSetting::where('type', $type)->first();

            if ($business_settings === null) {
                $business_settings = new BusinessSetting();
                $business_settings->type = $type;
            }

            // Maintenance mode handling with additional safety checks
            if ($type == 'maintenance_mode') {
                if (env('DEMO_MODE') != 'On') {
                    if ($value == '1') {
                        Artisan::call('down', [
                            '--retry' => 60
                        ]);
                    } elseif ($value == '0') {
                        Artisan::call('up');
                    }
                }
            }

            $business_settings->value = $value;
            $business_settings->save();

            Artisan::call('cache:clear');

            return response()->json(1); // Return 1 as expected by the frontend
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Activation Settings Update Error: ' . $e->getMessage());

            return response()->json(0, 500); // Return 0 on error as expected by the frontend
        }
    }

    public function updatePaymentActivationSettings(Request $request)
    {
        $payment_method = PaymentMethod::findOrFail($request->id);
        $payment_method->active = $request->value;
        $payment_method->save();

        Artisan::call('cache:clear');
        return 1;
    }

    public function updateActivationSettingsInEnv($request)
    {
        if ($request->type == 'FORCE_HTTPS' && $request->value == '1') {
            $this->overWriteEnvFile($request->type, 'On');

            if (strpos(env('APP_URL'), 'http:') !== FALSE) {
                $this->overWriteEnvFile('APP_URL', str_replace("http:", "https:", env('APP_URL')));
            }
        } elseif ($request->type == 'FORCE_HTTPS' && $request->value == '0') {
            $this->overWriteEnvFile($request->type, 'Off');
            if (strpos(env('APP_URL'), 'https:') !== FALSE) {
                $this->overWriteEnvFile('APP_URL', str_replace("https:", "http:", env('APP_URL')));
            }
        } elseif ($request->type == 'FILESYSTEM_DRIVER') {
            $this->overWriteEnvFile($request->type, $request->value);
        }

        return 1;
    }

    public function vendor_commission(Request $request)
    {
        return view('backend.sellers.seller_commission.index');
    }

    public function shipping_configuration(Request $request)
    {
        return view('backend.setup_configurations.shipping_configuration.index');
    }

    public function shipping_configuration_update(Request $request)
    {
        $business_settings = BusinessSetting::where('type', $request->type)->first();
        $business_settings->value = $request[$request->type];
        $business_settings->save();

        Artisan::call('cache:clear');
        flash(translate('Shipping Method updated successfully'))->success();
        return back();
    }

    public function order_configuration()
    {
        return view('backend.setup_configurations.order_configuration.index');
    }

    public function import_data(Request $request)
    {
        flash(translate('Demo data uploaded successfully'))->success();
        return back();
    }

    public function test_email(Request $request)
    {
        try {
            // Get email settings from business settings
            $mailFromAddress = get_setting('mail_from_address');
            $adminEmail = get_setting('admin_email');
            $smtpHost = get_setting('smtp_host');
            $smtpPort = get_setting('smtp_port');
            $smtpUsername = get_setting('smtp_username');
            $smtpPassword = get_setting('smtp_password');
            $smtpEncryption = get_setting('smtp_encryption', 'tls');

            // Comprehensive validation
            $missingSettings = [];
            if (empty($mailFromAddress)) $missingSettings[] = 'Sender Email Address';
            if (empty($adminEmail)) $missingSettings[] = 'Admin Email Address';
            if (empty($smtpHost)) $missingSettings[] = 'SMTP Host';
            if (empty($smtpPort)) $missingSettings[] = 'SMTP Port';
            if (empty($smtpUsername)) $missingSettings[] = 'SMTP Username';
            if (empty($smtpPassword)) $missingSettings[] = 'SMTP Password';

            if (!empty($missingSettings)) {
                return response()->json([
                    'success' => false,
                    'message' => __('The following settings are missing:') . ' ' . implode(', ', $missingSettings),
                ]);
            }

            // Override ALL mail configuration with business settings
            config([
                'mail.default' => 'smtp',
                'mail.mailers.smtp.host' => $smtpHost,
                'mail.mailers.smtp.port' => $smtpPort,
                'mail.mailers.smtp.username' => $smtpUsername,
                'mail.mailers.smtp.password' => $smtpPassword,
                'mail.mailers.smtp.encryption' => $smtpEncryption,
                'mail.from.address' => $mailFromAddress,
                'mail.from.name' => get_setting('site_name', env('APP_NAME'))
            ]);

            // Attempt to send email
            \Mail::raw(__('This is a test email to verify your SMTP configuration.'), function($message) use ($adminEmail, $mailFromAddress) {
                $message->to($adminEmail)
                        ->subject(__('Test Email Configuration'))
                        ->from($mailFromAddress, get_setting('site_name', env('APP_NAME')));
            });

            return response()->json([
                'success' => true,
                'message' => __('Test email sent successfully to') . ' ' . $adminEmail,
            ]);
        } catch (\Swift_TransportException $e) {
            // Specific Swift Mailer transport errors
            return response()->json([
                'success' => false,
                'message' => __('SMTP Connection Error:') . ' ' . $e->getMessage(),
            ]);
        } catch (\Exception $e) {
            // Catch-all for other potential errors
            return response()->json([
                'success' => false,
                'message' => __('Email sending failed:') . ' ' . $e->getMessage(),
            ]);
        }
    }

    public function tailwind_config(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();
        CoreComponentRepository::initializeCache();

        // Get current tailwind config if it exists
        $tailwind_config = BusinessSetting::where('type', 'tailwind_colors')->first();
        $colors = $tailwind_config ? json_decode($tailwind_config->value, true) : [
            'primary' => '#3490dc',
            'secondary' => '#ffed4a',
            'text' => '#333333',
            'background' => '#ffffff',
        ];

        return view('backend.setup_configurations.tailwind_config', compact('colors'));
    }

    /**
     * Update Tailwind colors configuration
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function tailwind_config_update(Request $request)
    {
        // Log the request for debugging
        \Log::info('Tailwind Config Update Request:', [
            'all_data' => $request->all(),
            'colors' => $request->input('colors')
        ]);

        try {
            // Less strict validation to accept various color formats
            $request->validate([
                'colors' => 'required|array',
                'colors.primary' => 'required|string',
                'colors.secondary' => 'required|string',
                'colors.accent' => 'required|string',
                'colors.text' => 'required|string',
                'colors.background' => 'required|string',
            ]);

            // Get existing colors
            $currentColors = BusinessSetting::where('type', 'tailwind_colors')->first();
            $existingColors = $currentColors ? json_decode($currentColors->value, true) : [];

            // Get the submitted colors
            $submittedColors = $request->input('colors');

            // Format and sanitize colors
            $formattedColors = [];
            $defaultColors = [
                'primary' => '#3490dc',
                'secondary' => '#ffed4a',
                'accent' => '#f6993f',
                'text' => '#333333',
                'background' => '#ffffff'
            ];

            // Process each color, ensuring proper format
            foreach (['primary', 'secondary', 'accent', 'text', 'background'] as $colorKey) {
                $colorValue = $submittedColors[$colorKey] ??
                              $existingColors[$colorKey] ??
                              $defaultColors[$colorKey];

                // Ensure color starts with #
                if (!empty($colorValue) && substr($colorValue, 0, 1) !== '#') {
                    $colorValue = '#' . $colorValue;
                }

                // Validate it's a proper hex color
                if (!empty($colorValue) && preg_match('/^#([0-9A-Fa-f]{3}){1,2}$/', $colorValue)) {
                    // Convert 3-digit hex to 6-digit if needed
                    if (strlen($colorValue) === 4) {
                        $r = substr($colorValue, 1, 1);
                        $g = substr($colorValue, 2, 1);
                        $b = substr($colorValue, 3, 1);
                        $colorValue = "#{$r}{$r}{$g}{$g}{$b}{$b}";
                    }
                    $formattedColors[$colorKey] = $colorValue;
                } else {
                    // Fallback to default or existing if invalid
                    $formattedColors[$colorKey] = $existingColors[$colorKey] ?? $defaultColors[$colorKey];
                }
            }

            // Log the sanitized colors
            \Log::info('Sanitized colors for saving:', $formattedColors);

            // Save to database
            $businessSetting = BusinessSetting::where('type', 'tailwind_colors')->first();
            if (!$businessSetting) {
                $businessSetting = new BusinessSetting();
                $businessSetting->type = 'tailwind_colors';
            }
            $businessSetting->value = json_encode($formattedColors);
            $businessSetting->save();

            // Generate and save CSS file
            $cssGenerated = $this->updateTailwindConfig($formattedColors);

            if (!$cssGenerated) {
                \Log::warning('CSS file could not be written. Check directory permissions.');
            }

            // Clear cache
            Artisan::call('cache:clear');

            // Add success message using session flash
            return back()->with('success', 'Tailwind colors updated successfully');
        } catch (\Exception $e) {
            // Log error with details
            \Log::error('Tailwind color update error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return with error message
            return back()->with('error', 'Failed to update colors: ' . $e->getMessage());
        }
    }

    /**
     * Theme settings page
     */
    public function theme_settings(Request $request)
    {
        // Available themes (add more as needed)
        $themes = [
            'modern' => 'Modern',
            'octopus' => 'Octopus',
        ];

        // Get current theme
        $active_theme = get_setting('active_theme', 'default');

        // Get current tailwind colors
        $tailwind_config = BusinessSetting::where('type', 'tailwind_colors')->first();
        $tailwind_colors = $tailwind_config
            ? json_decode($tailwind_config->value, true)
            : [
                'primary' => '#3490dc',
                'secondary' => '#ffed4a',
                'accent' => '#f6993f',
                'text' => '#333333',
                'background' => '#ffffff',
            ];

        return view('backend.setup_configurations.theme_settings', compact('themes', 'active_theme', 'tailwind_colors'));
    }

    /**
     * Update active theme
     */
    public function update_theme(Request $request)
    {
        if (env('DEMO_MODE') == 'On') {
            flash(translate('Sorry! Demo mode is active.'))->error();
            return back();
        }

        // Validate theme name
        $request->validate([
            'theme' => 'required|in:modern,octopus',
        ]);

        // Update the setting
        $business_settings = BusinessSetting::where('type', 'active_theme')->first();
        if (!$business_settings) {
            $business_settings = new BusinessSetting;
            $business_settings->type = 'active_theme';
        }
        $business_settings->value = $request->theme;
        $business_settings->save();

        // Clear cache
        Artisan::call('cache:clear');

        flash(translate('Theme updated successfully.'))->success();
        return back();
    }




}
