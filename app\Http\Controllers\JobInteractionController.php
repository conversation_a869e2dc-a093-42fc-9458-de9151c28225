<?php

namespace App\Http\Controllers;

use App\Models\JobFavorite;
use App\Models\JobListing;
use App\Models\JobShortlist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class JobInteractionController extends Controller
{
    /**
     * Toggle job favorite status
     */
    public function toggleFavorite(Request $request, $jobId)
    {
        try {
            // Validate user is authenticated
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please login to favorite jobs.'
                ], 401);
            }

            // Validate job exists and is published
            $job = JobListing::where('id', $jobId)
                ->where('status', 'published')
                ->first();

            if (!$job) {
                return response()->json([
                    'success' => false,
                    'message' => 'Job not found.'
                ], 404);
            }

            $user = Auth::user();

            // Check if already favorited
            $favorite = JobFavorite::where('user_id', $user->id)
                ->where('job_listing_id', $jobId)
                ->first();

            if ($favorite) {
                // Remove from favorites
                $favorite->delete();
                $favorited = false;
                $message = 'Job removed from favorites.';
                Log::channel('jobs')->info('Job removed from favorites', [
                    'user_id' => $user->id,
                    'job_id' => $jobId,
                    'job_title' => $job->title
                ]);
            } else {
                // Add to favorites
                JobFavorite::create([
                    'user_id' => $user->id,
                    'job_listing_id' => $jobId,
                ]);
                $favorited = true;
                $message = 'Job added to favorites.';
                Log::channel('jobs')->info('Job added to favorites', [
                    'user_id' => $user->id,
                    'job_id' => $jobId,
                    'job_title' => $job->title
                ]);
            }

            return response()->json([
                'success' => true,
                'favorited' => $favorited,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            Log::channel('jobs')->error('Error toggling job favorite', [
                'user_id' => auth()->id(),
                'job_id' => $jobId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred. Please try again.'
            ], 500);
        }
    }

    /**
     * Toggle job shortlist status
     */
    public function toggleShortlist(Request $request, $jobId)
    {
        try {
            // Validate user is authenticated
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please login to shortlist jobs.'
                ], 401);
            }

            // Validate job exists and is published
            $job = JobListing::where('id', $jobId)
                ->where('status', 'published')
                ->first();

            if (!$job) {
                return response()->json([
                    'success' => false,
                    'message' => 'Job not found.'
                ], 404);
            }

            $user = Auth::user();

            // Check if already shortlisted
            $shortlist = JobShortlist::where('user_id', $user->id)
                ->where('job_listing_id', $jobId)
                ->first();

            if ($shortlist) {
                // Remove from shortlist
                $shortlist->delete();
                $shortlisted = false;
                $message = 'Job removed from shortlist.';
            } else {
                // Add to shortlist
                JobShortlist::create([
                    'user_id' => $user->id,
                    'job_listing_id' => $jobId,
                    'notes' => $request->input('notes', ''),
                ]);
                $shortlisted = true;
                $message = 'Job added to shortlist.';
            }

            return response()->json([
                'success' => true,
                'shortlisted' => $shortlisted,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred. Please try again.'
            ], 500);
        }
    }

    /**
     * Get user's favorite jobs (API endpoint)
     */
    public function getFavorites(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please login to view favorites.'
            ], 401);
        }

        $favorites = Auth::user()->favoritedJobs()
            ->with(['company', 'categories'])
            ->where('status', 'published')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $favorites
        ]);
    }

    /**
     * Show user's favorite jobs page
     */
    public function showFavorites()
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to view your favorites.');
        }

        $favorites = Auth::user()->favoritedJobs()
            ->with(['company', 'categories'])
            ->where('status', 'published')
            ->paginate(12);

        $theme = get_theme();
        return view("frontend.{$theme}.my-favorites", compact('favorites'));
    }

    /**
     * Get user's shortlisted jobs (API endpoint)
     */
    public function getShortlists(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please login to view shortlist.'
            ], 401);
        }

        $shortlists = Auth::user()->shortlistedJobs()
            ->with(['company', 'categories'])
            ->where('status', 'published')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $shortlists
        ]);
    }

    /**
     * Show user's shortlisted jobs page
     */
    public function showShortlists()
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to view your shortlists.');
        }

        $shortlists = Auth::user()->shortlistedJobs()
            ->with(['company', 'categories'])
            ->where('status', 'published')
            ->paginate(12);

        $theme = get_theme();
        return view("frontend.{$theme}.my-shortlists", compact('shortlists'));
    }
}
