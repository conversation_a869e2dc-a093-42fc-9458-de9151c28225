<?php

namespace App\Http\Controllers;

use App\Models\JobListing;
use App\Models\Category;
use App\Models\Company;
use App\Models\Page;
use App\Services\SeoService;
use App\Helpers\SecurityHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

/**
 * WebController - Cleaned up and dynamic version
 * Handles public frontend pages and job-related functionality
 */
class WebController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Homepage - Display featured content
     */
    public function index()
    {
        // Get featured job categories (limit to 6 for the homepage)
        $featuredCategories = Cache::remember('featured_categories', 600, function () {
            return Category::whereNull('parent_id')
                ->withCount(['jobListings' => function ($query) {
                    $query->where('status', 'published');
                }])
                ->orderBy('job_listings_count', 'desc')
                ->take(6)
                ->get();
        });

        // Get recent job listings (limit to 10 for the homepage)
        $recentJobs = Cache::remember('recent_jobs', 300, function () {
            return JobListing::with(['company:id,name,logo,slug', 'categories:id,name,slug'])
                ->select(['id', 'title', 'slug', 'description', 'location', 'job_type', 'salary_min', 'salary_max', 'experience_level', 'remote_option', 'views_count', 'created_at', 'company_id'])
                ->where('status', 'published')
                ->latest()
                ->take(10)
                ->get();
        });

        // Add user favorites and shortlists if authenticated
        if (auth()->check()) {
            $userFavorites = auth()->user()->favoritedJobs()->pluck('job_listings.id')->toArray();
            $userShortlists = auth()->user()->shortlistedJobs()->pluck('job_listings.id')->toArray();

            foreach ($recentJobs as $job) {
                $job->is_favorited = in_array($job->id, $userFavorites);
                $job->is_shortlisted = in_array($job->id, $userShortlists);
            }
        }

        // Get top companies/recruiters (companies with most job listings)
        $topRecruiters = Cache::remember('top_recruiters', 600, function () {
            return Company::select(['id', 'name', 'slug', 'logo'])
                ->withCount(['jobListings' => function ($query) {
                    $query->where('status', 'published');
                }])
                ->having('job_listings_count', '>', 0)
                ->orderBy('job_listings_count', 'desc')
                ->take(10)
                ->get();
        });

        // Get jobs by location (top 10 locations with job counts)
        $jobsByLocation = JobListing::select('location', \DB::raw('count(*) as count'))
            ->where('status', 'published')
            ->whereNotNull('location')
            ->where('location', '!=', '')
            ->groupBy('location')
            ->orderBy('count', 'desc')
            ->take(10)
            ->get();

        // SEO Data
        $seoData = $this->seoService->generateMetaTags(
            'Find Your Dream Job | JobOctopus',
            'Discover thousands of job opportunities across various industries. Connect with top employers and advance your career with JobOctopus.',
            'jobs, careers, employment, job search, recruitment, hiring, work opportunities'
        );

        // Structured Data
        $structuredData = $this->seoService->generateWebsiteStructuredData();

        $theme = get_theme();
        return view("frontend.{$theme}.index", compact('featuredCategories', 'recentJobs', 'topRecruiters', 'jobsByLocation', 'seoData', 'structuredData'));
    }

    /**
     * Display jobs by category
     */
    public function categoryJobs($slug)
    {
        $category = Category::where('slug', $slug)->firstOrFail();

        $jobs = JobListing::with(['company', 'categories'])
            ->whereHas('categories', function ($query) use ($category) {
                $query->where('categories.id', $category->id);
            })
            ->where('status', 'published')
            ->latest()
            ->paginate(20);

        // SEO Data
        $seoData = $this->seoService->generateCategorySeo($category);

        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Jobs', 'url' => url('/jobs')],
            ['name' => $category->name . ' Jobs', 'url' => '']
        ];

        // Structured Data
        $structuredData = $this->seoService->generateBreadcrumbStructuredData($breadcrumbs);

        $theme = get_theme();
        return view("frontend.{$theme}.category-jobs", compact('category', 'jobs', 'seoData', 'breadcrumbs', 'structuredData'));
    }

    /**
     * Display jobs by type (volunteering, casual, etc.)
     */
    public function jobsByType(Request $request)
    {
        $type = $request->route('type');

        $jobs = JobListing::with(['company', 'categories'])
            ->where('job_type', $type)
            ->where('status', 'published')
            ->latest()
            ->paginate(20);

        $title = ucfirst(str_replace('_', ' ', $type)) . ' Jobs';

        $theme = get_theme();
        return view("frontend.{$theme}.jobs-by-type", compact('jobs', 'type', 'title'));
    }

    /**
     * Search jobs with comprehensive filtering
     */
    public function searchJobs(Request $request)
    {
        // Validate and sanitize input
        $validated = $request->validate([
            'keywords' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'category' => 'nullable|integer|exists:categories,id',
            'job_type' => 'nullable|string|in:full_time,part_time,contract,freelance,internship,volunteering,casual,business,franchise,online',
            'salary_min' => 'nullable|integer|min:0',
            'salary_max' => 'nullable|integer|min:0',
            'experience_level' => 'nullable|string|in:entry,mid,senior,executive',
            'remote_option' => 'nullable|string|in:0,1,hybrid',
            'date_posted' => 'nullable|integer|in:1,7,30,90,180',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from'
        ]);

        // Build query
        $query = JobListing::with(['company:id,name,logo,slug', 'categories:id,name,slug'])
            ->where('status', 'published');

        $query = $this->applySearchFilters($query, $validated);

        $jobs = $query->paginate(12);

        // Add user favorites and shortlists if authenticated
        if (auth()->check()) {
            $userFavorites = auth()->user()->favoritedJobs()->pluck('job_listings.id')->toArray();
            $userShortlists = auth()->user()->shortlistedJobs()->pluck('job_listings.id')->toArray();

            foreach ($jobs as $job) {
                $job->is_favorited = in_array($job->id, $userFavorites);
                $job->is_shortlisted = in_array($job->id, $userShortlists);
            }
        }

        // Handle AJAX requests
        if ($request->ajax() || $request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => $jobs->items(),
                'pagination' => [
                    'current_page' => $jobs->currentPage(),
                    'last_page' => $jobs->lastPage(),
                    'per_page' => $jobs->perPage(),
                    'total' => $jobs->total(),
                ]
            ]);
        }

        // Get filter data for dropdowns
        $categories = Category::whereNull('parent_id')->get();
        $searchParams = $validated;

        $theme = get_theme();
        return view("frontend.{$theme}.search-results", compact('jobs', 'searchParams', 'categories'));
    }

    /**
     * Apply search filters to query
     */
    private function applySearchFilters($query, $validated)
    {

        // Keywords search with proper sanitization
        if (!empty($validated['keywords'])) {
            $keywords = SecurityHelper::sanitizeSearchQuery($validated['keywords']);

            // Check for suspicious patterns
            if (SecurityHelper::containsSuspiciousPatterns($keywords)) {
                SecurityHelper::logSecurityEvent('Suspicious search query detected', ['query' => $keywords]);
                abort(400, 'Invalid search query');
            }

            $query->where(function ($q) use ($keywords) {
                $q->where('title', 'like', '%' . $keywords . '%')
                  ->orWhere('description', 'like', '%' . $keywords . '%')
                  ->orWhereHas('company', function ($companyQuery) use ($keywords) {
                      $companyQuery->where('name', 'like', '%' . $keywords . '%');
                  });
            });
        }

        // Location filter with sanitization
        if (!empty($validated['location'])) {
            $location = SecurityHelper::sanitizeSearchQuery($validated['location']);
            $query->where('location', 'like', '%' . $location . '%');
        }

        // Job type filter
        if (!empty($validated['job_type'])) {
            $query->where('job_type', $validated['job_type']);
        }

        // Category filter
        if (!empty($validated['category'])) {
            $query->whereHas('categories', function ($q) use ($validated) {
                $q->where('categories.id', $validated['category']);
            });
        }

        // Experience level filter
        if (!empty($validated['experience_level'])) {
            $query->where('experience_level', $validated['experience_level']);
        }

        // Salary range filter
        if (!empty($validated['salary_min'])) {
            $query->where('salary_max', '>=', $validated['salary_min']);
        }
        if (!empty($validated['salary_max'])) {
            $query->where('salary_min', '<=', $validated['salary_max']);
        }

        // Remote work filter
        if (isset($validated['remote_option']) && $validated['remote_option'] !== '') {
            if ($validated['remote_option'] === 'hybrid') {
                $query->where('remote_option', true);
            } else {
                $query->where('remote_option', (bool)$validated['remote_option']);
            }
        }

        // Date posted filter
        if (!empty($validated['date_posted'])) {
            $daysAgo = (int)$validated['date_posted'];
            $query->where('created_at', '>=', now()->subDays($daysAgo));
        }

        // Date range filter
        if (!empty($validated['date_from'])) {
            $query->whereDate('created_at', '>=', $validated['date_from']);
        }
        if (!empty($validated['date_to'])) {
            $query->whereDate('created_at', '<=', $validated['date_to']);
        }

        // Sorting
        $sortBy = request()->get('sort_by', 'latest');
        switch ($sortBy) {
            case 'salary_high':
                $query->orderBy('salary_max', 'desc');
                break;
            case 'salary_low':
                $query->orderBy('salary_min', 'asc');
                break;
            case 'oldest':
                $query->oldest();
                break;
            default:
                $query->latest();
                break;
        }

        return $query;
    }

    /**
     * Display all jobs
     */
    public function allJobs()
    {
        $jobs = JobListing::with(['company', 'categories'])
            ->where('status', 'published')
            ->latest()
            ->paginate(20);

        $theme = get_theme();
        return view("frontend.{$theme}.all-jobs", compact('jobs'));
    }

    /**
     * Display all companies (public frontend page)
     */
    public function companies()
    {
        $companies = Company::withCount('jobListings')
            ->orderBy('job_listings_count', 'desc')
            ->paginate(20);

        $theme = get_theme();
        return view("frontend.{$theme}.companies", compact('companies'));
    }

    /**
     * Display company profile page
     */
    public function companyProfile($slug)
    {
        $company = Company::where('slug', $slug)->firstOrFail();

        // Get recent jobs from this company
        $recentJobs = JobListing::with(['categories'])
            ->where('company_id', $company->id)
            ->where('status', 'published')
            ->latest()
            ->take(5)
            ->get();

        // Get total job count
        $totalJobs = JobListing::where('company_id', $company->id)
            ->where('status', 'published')
            ->count();

        // SEO Data
        $seoData = $this->seoService->generateCompanySeo($company);

        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Companies', 'url' => url('/companies')],
            ['name' => $company->name, 'url' => '']
        ];

        // Structured Data
        $structuredData = [
            $this->seoService->generateOrganizationStructuredData($company),
            $this->seoService->generateBreadcrumbStructuredData($breadcrumbs)
        ];

        $theme = get_theme();
        return view("frontend.{$theme}.company-profile", compact('company', 'recentJobs', 'totalJobs', 'seoData', 'breadcrumbs', 'structuredData'));
    }

    /**
     * Display jobs for a specific company
     */
    public function companyJobs($slug)
    {
        $company = Company::where('slug', $slug)->firstOrFail();

        $jobs = JobListing::with(['categories'])
            ->where('company_id', $company->id)
            ->where('status', 'published')
            ->latest()
            ->paginate(20);

        $theme = get_theme();
        return view("frontend.{$theme}.company-jobs", compact('company', 'jobs'));
    }

    /**
     * Display individual job listing (public page)
     */
    public function jobDetail($slug)
    {
        $job = JobListing::where('slug', $slug)
            ->where('status', 'published')
            ->with(['company', 'categories'])
            ->firstOrFail();

        // Increment view count
        $job->increment('views_count');

        // Check if the user has already applied
        $hasApplied = false;
        if (auth()->check() && auth()->user()->isCandidate()) {
            $hasApplied = auth()->user()->applications()
                ->where('job_listing_id', $job->id)
                ->exists();
        }

        // Get similar jobs
        $similarJobs = JobListing::where('id', '!=', $job->id)
            ->where('status', 'published')
            ->whereHas('categories', function ($query) use ($job) {
                $query->whereIn('categories.id', $job->categories->pluck('id'));
            })
            ->with(['company', 'categories'])
            ->take(3)
            ->get();

        // SEO Data
        $seoData = $this->seoService->generateJobSeo($job);

        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Jobs', 'url' => url('/jobs')],
            ['name' => $job->title, 'url' => '']
        ];

        // Structured Data
        $structuredData = [
            $this->seoService->generateJobStructuredData($job),
            $this->seoService->generateBreadcrumbStructuredData($breadcrumbs)
        ];

        $theme = get_theme();
        return view("frontend.{$theme}.job-detail", compact('job', 'hasApplied', 'similarJobs', 'seoData', 'breadcrumbs', 'structuredData'));
    }

    /**
     * Handle dynamic pages - completely dynamic approach
     */
    public function dynamicPage($slug)
    {
        $theme = get_theme();

        // 1. First, try to find an exact category match
        $category = Category::where('slug', $slug)->first();
        if ($category) {
            return redirect()->route('category.jobs', $category->slug);
        }

        // 2. Try to find a category by name variations (handle legacy URLs)
        $searchTerms = [
            str_replace(['_', '-'], ' ', $slug),
            str_replace(['_jobs', '_job', '-jobs', '-job'], '', $slug),
            ucwords(str_replace(['_', '-'], ' ', $slug))
        ];

        foreach ($searchTerms as $term) {
            $category = Category::where('name', 'LIKE', "%{$term}%")
                ->orWhere('slug', 'LIKE', "%{$term}%")
                ->first();

            if ($category) {
                return redirect()->route('category.jobs', $category->slug);
            }
        }

        // 3. Check if there's a dynamic page in the database
        $page = Page::active()->where('slug', $slug)->first();
        if ($page) {
            return $this->renderDynamicPage($page, $theme);
        }

        // 3.5. Handle special dynamic pages with additional data
        switch ($slug) {
            case 'training':
                return $this->trainingPage($theme);
            case 'contact':
                return $this->contactPage($theme);
            case 'services':
                return $this->servicesPage($theme);
            case 'careers':
                return $this->careersPage($theme);
            case 'it-careers':
                return $this->itCareersPage($theme);
            case 'non-it-careers':
                return $this->nonItCareersPage($theme);
        }

        // 4. Try to find jobs by searching content (create virtual category)
        $searchTerm = str_replace(['_', '-'], ' ', $slug);
        $jobs = JobListing::with(['company', 'categories'])
            ->where('status', 'published')
            ->where(function($query) use ($searchTerm, $slug) {
                $query->where('title', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('description', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('job_type', 'LIKE', "%{$searchTerm}%")
                      ->orWhereHas('categories', function($q) use ($searchTerm) {
                          $q->where('name', 'LIKE', "%{$searchTerm}%");
                      });
            })
            ->latest()
            ->paginate(20);

        if ($jobs->count() > 0) {
            $virtualCategory = (object) [
                'name' => ucwords(str_replace(['_', '-'], ' ', $slug)),
                'slug' => $slug,
                'description' => "Find the best " . ucwords(str_replace(['_', '-'], ' ', $slug)) . " jobs and career opportunities."
            ];

            return view("frontend.{$theme}.category-jobs", compact('jobs'))
                ->with('category', $virtualCategory);
        }

        // 5. Check if a theme-specific view exists for this slug
        $viewPath = "frontend.{$theme}.{$slug}";
        if (view()->exists($viewPath)) {
            return view($viewPath, $this->getPageData($slug));
        }

        // 6. Handle legacy redirects dynamically
        $legacyRedirects = [
            'index1' => 'home',
            'search_page' => 'jobs.search',
            'jobs' => 'jobs.all',
            'companies' => 'frontend.companies',
        ];

        if (isset($legacyRedirects[$slug])) {
            return redirect()->route($legacyRedirects[$slug]);
        }

        // 7. If nothing found, redirect to home with message
        return redirect()->route('home')
            ->with('info', 'The requested page has been moved or no longer exists.');
    }

    /**
     * Render a dynamic page using the appropriate template
     */
    private function renderDynamicPage($page, $theme)
    {
        // Determine template to use
        $template = $page->template ?: 'default';

        // Try theme-specific template first
        $viewPaths = [
            "frontend.{$theme}.{$template}",
            "frontend.{$theme}.{$page->slug}",
            "frontend.{$theme}.default",
            "frontend.pages.{$template}",
            "frontend.pages.default"
        ];

        foreach ($viewPaths as $viewPath) {
            if (view()->exists($viewPath)) {
                return view($viewPath, compact('page'));
            }
        }

        // Fallback to basic page display
        return view("frontend.{$theme}.default", compact('page'));
    }

    /**
     * Training page with dynamic content
     */
    private function trainingPage($theme)
    {
        $page = Page::active()->where('slug', 'training')->first();
        $institutes = collect([
            ['name' => 'Tech Academy', 'description' => 'Leading technology training institute', 'courses' => 25],
            ['name' => 'Business School', 'description' => 'Professional business training', 'courses' => 18],
            ['name' => 'Digital Institute', 'description' => 'Digital marketing and design courses', 'courses' => 12]
        ]);

        return view("frontend.{$theme}.training", compact('page', 'institutes'));
    }

    /**
     * Contact page with dynamic content
     */
    private function contactPage($theme)
    {
        $page = Page::active()->where('slug', 'contact')->first();
        $contactInfo = [
            'email' => '<EMAIL>',
            'phone' => '+91 9876543210',
            'address' => '123 Business District, Tech City, India'
        ];

        return view("frontend.{$theme}.contact", compact('page', 'contactInfo'));
    }

    /**
     * Services page with dynamic content
     */
    private function servicesPage($theme)
    {
        $page = Page::active()->where('slug', 'services')->first();
        $services = collect([
            ['title' => 'Resume Writing', 'description' => 'Professional resume writing services'],
            ['title' => 'Interview Preparation', 'description' => 'Expert interview coaching'],
            ['title' => 'Career Guidance', 'description' => 'Personalized career advice']
        ]);

        return view("frontend.{$theme}.services", compact('page', 'services'));
    }

    /**
     * Careers page with dynamic content
     */
    private function careersPage($theme)
    {
        $page = Page::active()->where('slug', 'careers')->first();
        $careerPaths = collect([
            ['title' => 'IT Careers', 'description' => 'Technology career opportunities', 'jobs_count' => JobListing::whereHas('categories', function($q) { $q->where('name', 'LIKE', '%IT%'); })->count()],
            ['title' => 'Non-IT Careers', 'description' => 'Non-technology career paths', 'jobs_count' => JobListing::whereDoesntHave('categories', function($q) { $q->where('name', 'LIKE', '%IT%'); })->count()]
        ]);

        return view("frontend.{$theme}.careers", compact('page', 'careerPaths'));
    }

    /**
     * IT Careers page with dynamic content
     */
    private function itCareersPage($theme)
    {
        $itCategories = Category::where('name', 'LIKE', '%IT%')
            ->orWhere('name', 'LIKE', '%Technology%')
            ->orWhere('name', 'LIKE', '%Software%')
            ->orWhere('name', 'LIKE', '%Developer%')
            ->withCount('jobListings')
            ->get();

        $recentItJobs = JobListing::whereHas('categories', function($q) {
            $q->where('name', 'LIKE', '%IT%')
              ->orWhere('name', 'LIKE', '%Technology%')
              ->orWhere('name', 'LIKE', '%Software%');
        })->with('company')->latest()->take(10)->get();

        return view("frontend.{$theme}.it_careers", compact('itCategories', 'recentItJobs'));
    }

    /**
     * Non-IT Careers page with dynamic content
     */
    private function nonItCareersPage($theme)
    {
        $nonItCategories = Category::whereDoesntHave('jobListings', function($q) {
            $q->whereHas('categories', function($cat) {
                $cat->where('name', 'LIKE', '%IT%')
                    ->orWhere('name', 'LIKE', '%Technology%')
                    ->orWhere('name', 'LIKE', '%Software%');
            });
        })->withCount('jobListings')->get();

        $recentNonItJobs = JobListing::whereDoesntHave('categories', function($q) {
            $q->where('name', 'LIKE', '%IT%')
              ->orWhere('name', 'LIKE', '%Technology%')
              ->orWhere('name', 'LIKE', '%Software%');
        })->with('company')->latest()->take(10)->get();

        return view("frontend.{$theme}.non_it_careers", compact('nonItCategories', 'recentNonItJobs'));
    }

    /**
     * Get page data for any slug
     */
    private function getPageData($slug)
    {
        $data = [];

        switch ($slug) {
            case 'faq':
                $data['faqs'] = collect([
                    ['question' => 'How do I apply for jobs?', 'answer' => 'Click on any job listing and use the apply button.'],
                    ['question' => 'Is registration free?', 'answer' => 'Yes, registration is completely free for job seekers.']
                ]);
                break;

            case 'about':
                $data['stats'] = [
                    'total_jobs' => JobListing::count(),
                    'total_companies' => Company::count(),
                    'total_categories' => Category::count()
                ];
                break;
        }

        return $data;
    }
}
