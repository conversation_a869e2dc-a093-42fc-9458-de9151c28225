<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Candidate>
 */
class CandidateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->name();
        $username = strtolower(str_replace(' ', '', $name));
        
        return [
            'user_id' => User::factory(),
            'address' => $this->faker->address(),
            'resume_path' => 'resumes/' . $username . '_resume.pdf',
            'linkedin_url' => 'https://linkedin.com/in/' . strtolower(str_replace(' ', '-', $name)),
            'github_url' => 'https://github.com/' . $username,
            'portfolio_url' => 'https://' . $username . '.dev',
            'skills' => implode(', ', $this->faker->randomElements([
                'PHP', 'Laravel', 'JavaScript', 'React', 'Vue.js', 'Python', 'Java',
                'SQL', 'MySQL', 'AWS', 'Docker', 'Git', 'Agile', 'Project Management'
            ], $this->faker->numberBetween(3, 8))),
            'experience' => $this->faker->paragraph(),
            'education' => $this->faker->sentence(),
            'certifications' => $this->faker->optional(0.7)->sentence(),
            'languages' => 'English (Native), ' . $this->faker->randomElement(['Spanish', 'French', 'German', 'Chinese']) . ' (Conversational)',
            'additional_info' => $this->faker->optional(0.5)->paragraph(),
        ];
    }
}
