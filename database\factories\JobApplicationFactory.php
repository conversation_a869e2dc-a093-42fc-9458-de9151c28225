<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\JobListing;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JobApplication>
 */
class JobApplicationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $statuses = ['pending', 'reviewed', 'shortlisted', 'interviewed', 'offered', 'hired', 'rejected'];
        
        return [
            'user_id' => User::factory(),
            'job_listing_id' => JobListing::factory(),
            'cover_letter' => $this->faker->paragraphs(3, true),
            'resume' => 'resumes/' . $this->faker->slug() . '.pdf',
            'status' => $this->faker->randomElement($statuses),
            'notes' => $this->faker->optional(0.3)->sentence(),
            'additional_documents' => $this->faker->optional(0.2)->randomElements(['portfolio.pdf', 'certificates.pdf', 'references.pdf'], 2),
            'answers' => [
                'Why do you want to work here?' => $this->faker->paragraph(),
                'What makes you a good fit?' => $this->faker->paragraph(),
                'Salary expectations?' => '$' . $this->faker->numberBetween(40000, 120000),
            ],
        ];
    }
}
