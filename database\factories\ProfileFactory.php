<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Profile>
 */
class ProfileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $jobTitles = [
            'Software Engineer', 'Marketing Manager', 'Data Scientist', 'UX Designer',
            'Project Manager', 'Financial Analyst', 'DevOps Engineer', 'HR Specialist',
            'Sales Representative', 'Content Writer', 'Business Analyst', 'Product Manager'
        ];

        $skills = [
            'PHP', 'Laravel', 'JavaScript', 'React', 'Vue.js', 'Python', 'Java', 'C++',
            'SQL', 'MySQL', 'PostgreSQL', 'MongoDB', 'AWS', 'Docker', 'Kubernetes',
            'Git', 'Agile', 'Scrum', 'Project Management', 'Digital Marketing', 'SEO',
            'Content Strategy', 'Adobe Creative Suite', 'Figma', 'Sketch'
        ];

        return [
            'user_id' => User::factory(),
            'title' => $this->faker->randomElement($jobTitles),
            'bio' => $this->faker->paragraphs(2, true),
            'resume' => 'resumes/' . $this->faker->slug() . '.pdf',
            'skills' => $this->faker->randomElements($skills, $this->faker->numberBetween(3, 8)),
            'experience' => [
                [
                    'company' => $this->faker->company(),
                    'position' => $this->faker->randomElement($jobTitles),
                    'duration' => $this->faker->year() . '-' . $this->faker->year(),
                    'description' => $this->faker->paragraph(),
                ]
            ],
            'education' => [
                [
                    'institution' => $this->faker->randomElement(['Harvard University', 'MIT', 'Stanford University', 'UC Berkeley']),
                    'degree' => $this->faker->randomElement(['Bachelor\'s Degree', 'Master\'s Degree', 'PhD']),
                    'field' => $this->faker->randomElement(['Computer Science', 'Business', 'Engineering', 'Marketing']),
                    'year' => $this->faker->year(),
                ]
            ],
            'certifications' => $this->faker->optional(0.7)->randomElements([
                'AWS Certified Solutions Architect',
                'Google Analytics Certified',
                'PMP Certification',
                'Scrum Master Certification',
                'Adobe Certified Expert'
            ], 2),
            'languages' => ['English (Native)', 'Spanish (Conversational)'],
            'location' => $this->faker->city() . ', ' . $this->faker->stateAbbr(),
            'availability' => $this->faker->randomElement(['Available', 'Available in 2 weeks', 'Available in 1 month']),
            'salary_expectation' => $this->faker->numberBetween(40000, 150000),
            'job_type_preference' => $this->faker->randomElements(['Full-time', 'Part-time', 'Contract', 'Remote'], 2),
            'profile_visibility' => $this->faker->randomElement(['public', 'private', 'limited']),
        ];
    }
}
