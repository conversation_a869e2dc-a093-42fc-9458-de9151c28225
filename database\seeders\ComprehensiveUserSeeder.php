<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Company;
use App\Models\Profile;
use App\Models\Candidate;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class ComprehensiveUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin
        $superadmin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'phone' => '******-0001',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);
        $superadmin->assignRole('superadmin');

        // Create Employers with Companies
        $this->createEmployers();

        // Create Candidates with Profiles
        $this->createCandidates();
    }

    private function createEmployers(): void
    {
        $companies = [
            [
                'name' => 'TechCorp Solutions',
                'email' => '<EMAIL>',
                'industry' => 'Technology',
                'size' => '201-500',
                'description' => 'Leading technology solutions provider specializing in cloud computing and AI.',
                'website' => 'https://techcorp.com',
                'location' => 'San Francisco, CA',
            ],
            [
                'name' => 'FinanceFirst Bank',
                'email' => '<EMAIL>',
                'industry' => 'Finance',
                'size' => '1000+',
                'description' => 'Premier financial institution offering comprehensive banking services.',
                'website' => 'https://financefirst.com',
                'location' => 'New York, NY',
            ],
            [
                'name' => 'HealthPlus Medical',
                'email' => '<EMAIL>',
                'industry' => 'Healthcare',
                'size' => '501-1000',
                'description' => 'Advanced healthcare provider with cutting-edge medical technology.',
                'website' => 'https://healthplus.com',
                'location' => 'Boston, MA',
            ],
            [
                'name' => 'EduTech Learning',
                'email' => '<EMAIL>',
                'industry' => 'Education',
                'size' => '51-200',
                'description' => 'Innovative educational technology company transforming learning.',
                'website' => 'https://edutech.com',
                'location' => 'Austin, TX',
            ],
            [
                'name' => 'RetailMax Inc',
                'email' => '<EMAIL>',
                'industry' => 'Retail',
                'size' => '1000+',
                'description' => 'Leading retail chain with nationwide presence.',
                'website' => 'https://retailmax.com',
                'location' => 'Chicago, IL',
            ],
            [
                'name' => 'ManufacturePro',
                'email' => '<EMAIL>',
                'industry' => 'Manufacturing',
                'size' => '201-500',
                'description' => 'Advanced manufacturing company specializing in automotive parts.',
                'website' => 'https://manufacturepro.com',
                'location' => 'Detroit, MI',
            ],
            [
                'name' => 'StartupHub',
                'email' => '<EMAIL>',
                'industry' => 'Technology',
                'size' => '11-50',
                'description' => 'Fast-growing startup focused on mobile app development.',
                'website' => 'https://startuphub.com',
                'location' => 'Seattle, WA',
            ],
            [
                'name' => 'ConsultingPro',
                'email' => '<EMAIL>',
                'industry' => 'Business',
                'size' => '51-200',
                'description' => 'Strategic consulting firm helping businesses transform.',
                'website' => 'https://consultingpro.com',
                'location' => 'Washington, DC',
            ],
            [
                'name' => 'CreativeAgency',
                'email' => '<EMAIL>',
                'industry' => 'Design',
                'size' => '11-50',
                'description' => 'Full-service creative agency specializing in digital marketing.',
                'website' => 'https://creativeagency.com',
                'location' => 'Los Angeles, CA',
            ],
            [
                'name' => 'GreenEnergy Corp',
                'email' => '<EMAIL>',
                'industry' => 'Energy',
                'size' => '201-500',
                'description' => 'Renewable energy company leading the green revolution.',
                'website' => 'https://greenenergy.com',
                'location' => 'Denver, CO',
            ],
        ];

        foreach ($companies as $index => $companyData) {
            $user = User::create([
                'name' => $companyData['name'] . ' HR',
                'email' => $companyData['email'],
                'phone' => '******-' . str_pad($index + 10, 4, '0', STR_PAD_LEFT),
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]);
            
            $user->assignRole('employer');
            
            Company::create([
                'user_id' => $user->id,
                'name' => $companyData['name'],
                'industry' => $companyData['industry'],
                'size' => $companyData['size'],
                'description' => $companyData['description'],
                'website' => $companyData['website'],
                'location' => $companyData['location'],
                'address' => fake()->address(),
                'phone' => $user->phone,
                'email' => $user->email,
                'founded_year' => fake()->numberBetween(1990, 2020),
                'social_media' => [
                    'linkedin' => 'https://linkedin.com/company/' . strtolower(str_replace(' ', '-', $companyData['name'])),
                    'twitter' => 'https://twitter.com/' . strtolower(str_replace(' ', '', $companyData['name'])),
                    'facebook' => 'https://facebook.com/' . strtolower(str_replace(' ', '', $companyData['name'])),
                ],
            ]);
        }
    }

    private function createCandidates(): void
    {
        $candidates = [
            ['name' => 'John Smith', 'title' => 'Software Engineer', 'skills' => ['PHP', 'Laravel', 'JavaScript', 'React']],
            ['name' => 'Sarah Johnson', 'title' => 'Marketing Manager', 'skills' => ['Digital Marketing', 'SEO', 'Content Strategy']],
            ['name' => 'Michael Brown', 'title' => 'Data Scientist', 'skills' => ['Python', 'Machine Learning', 'SQL', 'TensorFlow']],
            ['name' => 'Emily Davis', 'title' => 'UX Designer', 'skills' => ['Figma', 'User Research', 'Prototyping', 'Adobe Creative Suite']],
            ['name' => 'David Wilson', 'title' => 'Project Manager', 'skills' => ['Agile', 'Scrum', 'JIRA', 'Team Leadership']],
            ['name' => 'Lisa Anderson', 'title' => 'Financial Analyst', 'skills' => ['Excel', 'Financial Modeling', 'SQL', 'Tableau']],
            ['name' => 'Robert Taylor', 'title' => 'DevOps Engineer', 'skills' => ['AWS', 'Docker', 'Kubernetes', 'CI/CD']],
            ['name' => 'Jennifer Martinez', 'title' => 'HR Specialist', 'skills' => ['Recruitment', 'Employee Relations', 'HRIS', 'Training']],
            ['name' => 'Christopher Lee', 'title' => 'Sales Representative', 'skills' => ['CRM', 'Lead Generation', 'Negotiation', 'Customer Relations']],
            ['name' => 'Amanda White', 'title' => 'Content Writer', 'skills' => ['Content Creation', 'SEO Writing', 'WordPress', 'Social Media']],
            ['name' => 'James Garcia', 'title' => 'Network Administrator', 'skills' => ['Cisco', 'Network Security', 'Windows Server', 'Linux']],
            ['name' => 'Michelle Rodriguez', 'title' => 'Business Analyst', 'skills' => ['Requirements Analysis', 'Process Improvement', 'SQL', 'Visio']],
            ['name' => 'Kevin Thompson', 'title' => 'Mobile Developer', 'skills' => ['React Native', 'Flutter', 'iOS', 'Android']],
            ['name' => 'Nicole Clark', 'title' => 'Graphic Designer', 'skills' => ['Adobe Photoshop', 'Illustrator', 'InDesign', 'Branding']],
            ['name' => 'Daniel Lewis', 'title' => 'Quality Assurance Engineer', 'skills' => ['Test Automation', 'Selenium', 'JIRA', 'API Testing']],
            ['name' => 'Rachel Walker', 'title' => 'Operations Manager', 'skills' => ['Process Optimization', 'Supply Chain', 'Lean Six Sigma']],
            ['name' => 'Mark Hall', 'title' => 'Cybersecurity Specialist', 'skills' => ['Penetration Testing', 'CISSP', 'Incident Response']],
            ['name' => 'Stephanie Young', 'title' => 'Product Manager', 'skills' => ['Product Strategy', 'Roadmapping', 'User Stories', 'Analytics']],
            ['name' => 'Andrew King', 'title' => 'Database Administrator', 'skills' => ['MySQL', 'PostgreSQL', 'Database Optimization', 'Backup Recovery']],
            ['name' => 'Jessica Wright', 'title' => 'Customer Success Manager', 'skills' => ['Customer Retention', 'Account Management', 'CRM', 'Analytics']],
        ];

        foreach ($candidates as $index => $candidateData) {
            $user = User::create([
                'name' => $candidateData['name'],
                'email' => 'candidate' . ($index + 1) . '@example.com',
                'phone' => '******-' . str_pad($index + 100, 4, '0', STR_PAD_LEFT),
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]);
            
            $user->assignRole('candidate');
            
            // Create Profile
            Profile::create([
                'user_id' => $user->id,
                'title' => $candidateData['title'],
                'bio' => 'Experienced ' . $candidateData['title'] . ' with a passion for excellence and innovation.',
                'skills' => $candidateData['skills'],
                'experience' => [
                    [
                        'company' => fake()->company(),
                        'position' => $candidateData['title'],
                        'duration' => '2020-2024',
                        'description' => 'Led multiple projects and contributed to team success.',
                    ]
                ],
                'education' => [
                    [
                        'institution' => fake()->randomElement(['Harvard University', 'MIT', 'Stanford University', 'UC Berkeley']),
                        'degree' => 'Bachelor\'s Degree',
                        'field' => fake()->randomElement(['Computer Science', 'Business', 'Engineering', 'Marketing']),
                        'year' => fake()->numberBetween(2015, 2020),
                    ]
                ],
                'location' => fake()->city() . ', ' . fake()->stateAbbr(),
                'availability' => 'Available',
                'salary_expectation' => fake()->numberBetween(50000, 150000),
                'job_type_preference' => ['Full-time'],
                'profile_visibility' => 'public',
            ]);
            
            // Create Candidate record
            Candidate::create([
                'user_id' => $user->id,
                'address' => fake()->address(),
                'linkedin_url' => 'https://linkedin.com/in/' . strtolower(str_replace(' ', '-', $candidateData['name'])),
                'github_url' => 'https://github.com/' . strtolower(str_replace(' ', '', $candidateData['name'])),
                'portfolio_url' => 'https://' . strtolower(str_replace(' ', '', $candidateData['name'])) . '.dev',
                'skills' => implode(', ', $candidateData['skills']),
                'experience' => 'Experienced professional with proven track record.',
                'education' => 'Bachelor\'s degree in relevant field.',
                'certifications' => 'Industry-relevant certifications.',
                'languages' => 'English (Native), Spanish (Conversational)',
            ]);
        }
    }
}
