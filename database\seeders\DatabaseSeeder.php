<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting JobOctopus Database Seeding...');

        // Seed roles and permissions first
        $this->command->info('📋 Seeding roles and permissions...');
        $this->call(RolesAndPermissionsSeeder::class);

        // Seed categories
        $this->command->info('📂 Seeding job categories...');
        $this->call(CategorySeeder::class);

        // Seed comprehensive user data
        $this->command->info('👥 Seeding users and profiles...');
        $this->call(ComprehensiveUserSeeder::class);

        // Seed job listings with applications
        $this->command->info('💼 Seeding job listings...');
        $this->call(JobListingSeeder::class);

        // Seed job applications and interactions
        $this->command->info('📝 Seeding job applications and interactions...');
        $this->call(JobApplicationSeeder::class);

        // Seed messages between users
        $this->command->info('💬 Seeding messages...');
        $this->call(MessageSeeder::class);

        // Seed dynamic pages
        $this->command->info('📄 Seeding pages...');
        $this->call(PageSeeder::class);
        $this->call(DynamicPagesSeeder::class);
        $this->call(OctopusPagesSeeder::class);

        $this->command->info('✅ Database seeding completed successfully!');
        $this->command->info('');
        $this->command->info('🔑 Test Accounts Created:');
        $this->command->info('   Super Admin: <EMAIL> / password');
        $this->command->info('   Employers: employer1@example.<NAME_EMAIL> / password');
        $this->command->info('   Candidates: candidate1@example.<NAME_EMAIL> / password');
        $this->command->info('');
        $this->command->info('📊 Data Summary:');
        $this->command->info('   - 31 Users (1 Admin, 10 Employers, 20 Candidates)');
        $this->command->info('   - 10 Companies with detailed profiles');
        $this->command->info('   - 50+ Job listings across various categories');
        $this->command->info('   - 100+ Job applications');
        $this->command->info('   - Favorites, shortlists, and messages');
        $this->command->info('   - Complete job portal ecosystem ready for testing');
    }
}
