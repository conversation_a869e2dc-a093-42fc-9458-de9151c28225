<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting Essential JobOctopus Database Seeding...');

        // Seed roles and permissions first (Essential)
        $this->command->info('📋 Seeding roles and permissions...');
        $this->call(RolesAndPermissionsSeeder::class);

        // Seed job categories (Essential)
        $this->command->info('📂 Seeding job categories...');
        $this->call(CategorySeeder::class);

        // Create essential admin user
        $this->command->info('� Creating admin user...');
        $superadmin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'phone' => '******-0001',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);
        $superadmin->assignRole('superadmin');

        // Seed essential pages (Essential)
        $this->command->info('📄 Seeding essential pages...');
        $this->call(PagesSeeder::class);

        $this->command->info('✅ Essential database seeding completed!');
        $this->command->info('');
        $this->command->info('🔑 Admin Account Created:');
        $this->command->info('   Email: <EMAIL>');
        $this->command->info('   Password: password');
        $this->command->info('');
        $this->command->info('� Available Additional Seeders:');
        $this->command->info('   php artisan db:seed --class=ComprehensiveUserSeeder');
        $this->command->info('   php artisan db:seed --class=JobListingSeeder');
        $this->command->info('   php artisan db:seed --class=JobApplicationSeeder');
        $this->command->info('   php artisan db:seed --class=MessageSeeder');
        $this->command->info('   php artisan db:seed --class=DynamicPagesSeeder');
        $this->command->info('   php artisan db:seed --class=OctopusPagesSeeder');
    }
}
