<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\JobListing;
use App\Models\JobApplication;
use App\Models\JobFavorite;
use App\Models\JobShortlist;
use Illuminate\Database\Seeder;

class JobApplicationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $candidates = User::role('candidate')->get();
        $jobListings = JobListing::all();

        if ($candidates->isEmpty() || $jobListings->isEmpty()) {
            $this->command->warn('No candidates or job listings found. Skipping job applications seeding.');
            return;
        }

        // Create job applications
        $this->createJobApplications($candidates, $jobListings);
        
        // Create favorites and shortlists
        $this->createFavoritesAndShortlists($candidates, $jobListings);
    }

    private function createJobApplications($candidates, $jobListings): void
    {
        $applicationStatuses = ['pending', 'reviewed', 'shortlisted', 'interviewed', 'offered', 'hired', 'rejected'];
        $coverLetterTemplates = [
            "Dear Hiring Manager,\n\nI am writing to express my strong interest in the {position} position at {company}. With my background in {field}, I am confident that I would be a valuable addition to your team.\n\nMy experience includes:\n- Relevant skill 1\n- Relevant skill 2\n- Relevant skill 3\n\nI am excited about the opportunity to contribute to {company}'s success and would welcome the chance to discuss how my skills align with your needs.\n\nThank you for your consideration.\n\nSincerely,\n{name}",
            
            "Hello,\n\nI am excited to apply for the {position} role at {company}. Your company's reputation for innovation and excellence aligns perfectly with my career goals.\n\nIn my previous roles, I have:\n- Achieved significant results in similar positions\n- Developed expertise in relevant technologies\n- Led successful projects and initiatives\n\nI am particularly drawn to this opportunity because of {company}'s commitment to {value}. I believe my passion for {field} and proven track record make me an ideal candidate.\n\nI look forward to hearing from you.\n\nBest regards,\n{name}",
            
            "Dear {company} Team,\n\nI am thrilled to submit my application for the {position} position. As a dedicated professional with expertise in {field}, I am eager to bring my skills to your dynamic team.\n\nKey highlights of my background:\n- Strong technical skills in relevant areas\n- Proven ability to work in fast-paced environments\n- Excellent communication and collaboration skills\n\nI am impressed by {company}'s innovative approach and would be honored to contribute to your continued success.\n\nThank you for your time and consideration.\n\nWarm regards,\n{name}"
        ];

        $applicationCount = 0;
        $targetApplications = 120; // Target number of applications

        foreach ($candidates as $candidate) {
            // Each candidate applies to 3-8 jobs
            $applicationsPerCandidate = fake()->numberBetween(3, 8);
            $candidateJobListings = $jobListings->random($applicationsPerCandidate);

            foreach ($candidateJobListings as $jobListing) {
                if ($applicationCount >= $targetApplications) {
                    break 2;
                }

                // Check if application already exists
                if (JobApplication::where('user_id', $candidate->id)
                    ->where('job_listing_id', $jobListing->id)
                    ->exists()) {
                    continue;
                }

                $coverLetter = str_replace(
                    ['{position}', '{company}', '{field}', '{name}', '{value}'],
                    [
                        $jobListing->title,
                        $jobListing->company->name,
                        fake()->randomElement(['technology', 'business', 'marketing', 'design', 'finance']),
                        $candidate->name,
                        fake()->randomElement(['innovation', 'excellence', 'customer satisfaction', 'quality'])
                    ],
                    fake()->randomElement($coverLetterTemplates)
                );

                $status = fake()->randomElement($applicationStatuses);
                $createdAt = fake()->dateTimeBetween('-3 months', 'now');

                JobApplication::create([
                    'user_id' => $candidate->id,
                    'job_listing_id' => $jobListing->id,
                    'cover_letter' => $coverLetter,
                    'resume' => 'resumes/' . strtolower(str_replace(' ', '_', $candidate->name)) . '_resume.pdf',
                    'status' => $status,
                    'notes' => $status === 'rejected' ? 'Thank you for your interest. We have decided to move forward with other candidates.' : 
                              ($status === 'hired' ? 'Excellent candidate. Welcome to the team!' : null),
                    'additional_documents' => fake()->boolean(30) ? ['portfolio.pdf', 'certificates.pdf'] : null,
                    'answers' => [
                        'Why do you want to work here?' => 'I am passionate about ' . $jobListing->company->industry . ' and believe this role aligns perfectly with my career goals.',
                        'What makes you a good fit?' => 'My experience and skills directly match the requirements outlined in the job description.',
                        'Salary expectations?' => '$' . fake()->numberBetween(50000, 120000) . ' annually'
                    ],
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt,
                ]);

                $applicationCount++;
            }
        }

        $this->command->info("Created {$applicationCount} job applications");
    }

    private function createFavoritesAndShortlists($candidates, $jobListings): void
    {
        $favoritesCount = 0;
        $shortlistsCount = 0;

        foreach ($candidates as $candidate) {
            // Each candidate favorites 2-10 jobs
            $favoritesPerCandidate = fake()->numberBetween(2, 10);
            $favoriteJobs = $jobListings->random($favoritesPerCandidate);

            foreach ($favoriteJobs as $job) {
                if (!JobFavorite::where('user_id', $candidate->id)
                    ->where('job_listing_id', $job->id)
                    ->exists()) {
                    
                    JobFavorite::create([
                        'user_id' => $candidate->id,
                        'job_listing_id' => $job->id,
                        'created_at' => fake()->dateTimeBetween('-2 months', 'now'),
                    ]);
                    $favoritesCount++;
                }
            }

            // Each candidate shortlists 1-5 jobs
            $shortlistsPerCandidate = fake()->numberBetween(1, 5);
            $shortlistJobs = $jobListings->random($shortlistsPerCandidate);

            foreach ($shortlistJobs as $job) {
                if (!JobShortlist::where('user_id', $candidate->id)
                    ->where('job_listing_id', $job->id)
                    ->exists()) {
                    
                    JobShortlist::create([
                        'user_id' => $candidate->id,
                        'job_listing_id' => $job->id,
                        'notes' => fake()->randomElement([
                            'Interesting opportunity, need to research more',
                            'Good salary range, matches my expectations',
                            'Company culture seems great',
                            'Perfect location for me',
                            'Skills match perfectly',
                            null
                        ]),
                        'created_at' => fake()->dateTimeBetween('-2 months', 'now'),
                    ]);
                    $shortlistsCount++;
                }
            }
        }

        $this->command->info("Created {$favoritesCount} job favorites");
        $this->command->info("Created {$shortlistsCount} job shortlists");
    }
}
