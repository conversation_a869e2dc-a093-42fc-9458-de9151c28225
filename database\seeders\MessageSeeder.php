<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Message;
use App\Models\JobListing;
use App\Models\JobApplication;
use Illuminate\Database\Seeder;

class MessageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $employers = User::role('employer')->get();
        $candidates = User::role('candidate')->get();
        $jobListings = JobListing::with('applications')->get();

        if ($employers->isEmpty() || $candidates->isEmpty()) {
            $this->command->warn('No employers or candidates found. Skipping messages seeding.');
            return;
        }

        // Create application-related messages
        $this->createApplicationMessages($employers, $candidates, $jobListings);
        
        // Create general inquiry messages
        $this->createGeneralMessages($employers, $candidates);
    }

    private function createApplicationMessages($employers, $candidates, $jobListings): void
    {
        $messageTemplates = [
            'application_received' => [
                'subject' => 'Application Received - {position}',
                'body' => "Dear {candidate_name},\n\nThank you for your interest in the {position} position at {company}. We have received your application and will review it carefully.\n\nWe will contact you within the next few days if your qualifications match our requirements.\n\nBest regards,\n{employer_name}\nHR Department"
            ],
            'interview_invitation' => [
                'subject' => 'Interview Invitation - {position}',
                'body' => "Dear {candidate_name},\n\nWe are pleased to invite you for an interview for the {position} position at {company}.\n\nPlease let us know your availability for the following time slots:\n- Monday 10:00 AM - 12:00 PM\n- Tuesday 2:00 PM - 4:00 PM\n- Wednesday 9:00 AM - 11:00 AM\n\nWe look forward to meeting you.\n\nBest regards,\n{employer_name}"
            ],
            'application_update' => [
                'subject' => 'Application Status Update - {position}',
                'body' => "Dear {candidate_name},\n\nWe wanted to update you on the status of your application for the {position} position.\n\nYour application has been reviewed and moved to the next stage of our selection process. We will be in touch soon with next steps.\n\nThank you for your patience.\n\nBest regards,\n{employer_name}"
            ],
            'candidate_inquiry' => [
                'subject' => 'Question about {position} Position',
                'body' => "Dear Hiring Manager,\n\nI hope this message finds you well. I recently applied for the {position} position at {company} and wanted to ask a few questions:\n\n1. What is the expected start date for this role?\n2. Are there opportunities for professional development?\n3. What does the typical career progression look like?\n\nI am very excited about this opportunity and look forward to hearing from you.\n\nBest regards,\n{candidate_name}"
            ],
            'thank_you' => [
                'subject' => 'Thank you for the interview - {position}',
                'body' => "Dear {employer_name},\n\nThank you for taking the time to interview me for the {position} position yesterday. I enjoyed our conversation and learning more about {company}.\n\nI am very interested in this role and believe my skills would be a great fit for your team. Please let me know if you need any additional information.\n\nI look forward to hearing about the next steps.\n\nBest regards,\n{candidate_name}"
            ]
        ];

        $messagesCount = 0;

        foreach ($jobListings as $jobListing) {
            $applications = $jobListing->applications;
            
            foreach ($applications->take(3) as $application) { // Limit to 3 applications per job for messages
                $employer = $jobListing->user;
                $candidate = $application->user;
                
                // 70% chance of having message exchange
                if (fake()->boolean(70)) {
                    $messageType = fake()->randomElement(['application_received', 'interview_invitation', 'application_update']);
                    $template = $messageTemplates[$messageType];
                    
                    // Employer sends initial message
                    $employerMessage = Message::create([
                        'sender_id' => $employer->id,
                        'recipient_id' => $candidate->id,
                        'subject' => str_replace(
                            ['{position}', '{company}'],
                            [$jobListing->title, $jobListing->company->name],
                            $template['subject']
                        ),
                        'body' => str_replace(
                            ['{candidate_name}', '{position}', '{company}', '{employer_name}'],
                            [$candidate->name, $jobListing->title, $jobListing->company->name, $employer->name],
                            $template['body']
                        ),
                        'related_job_id' => $jobListing->id,
                        'related_application_id' => $application->id,
                        'created_at' => fake()->dateTimeBetween($application->created_at, 'now'),
                    ]);
                    $messagesCount++;

                    // 60% chance candidate replies
                    if (fake()->boolean(60)) {
                        $replyType = fake()->randomElement(['candidate_inquiry', 'thank_you']);
                        $replyTemplate = $messageTemplates[$replyType];
                        
                        Message::create([
                            'sender_id' => $candidate->id,
                            'recipient_id' => $employer->id,
                            'subject' => 'Re: ' . $employerMessage->subject,
                            'body' => str_replace(
                                ['{candidate_name}', '{position}', '{company}', '{employer_name}'],
                                [$candidate->name, $jobListing->title, $jobListing->company->name, $employer->name],
                                $replyTemplate['body']
                            ),
                            'related_job_id' => $jobListing->id,
                            'related_application_id' => $application->id,
                            'parent_id' => $employerMessage->id,
                            'created_at' => fake()->dateTimeBetween($employerMessage->created_at, 'now'),
                        ]);
                        $messagesCount++;
                    }
                }
            }
        }

        $this->command->info("Created {$messagesCount} application-related messages");
    }

    private function createGeneralMessages($employers, $candidates): void
    {
        $generalTemplates = [
            'company_inquiry' => [
                'subject' => 'Inquiry about Career Opportunities',
                'body' => "Dear Hiring Team,\n\nI hope this message finds you well. I am very interested in potential career opportunities at your company.\n\nI have experience in {field} and would love to learn more about current or upcoming openings that might be a good fit for my background.\n\nCould we schedule a brief call to discuss potential opportunities?\n\nThank you for your time.\n\nBest regards,\n{candidate_name}"
            ],
            'networking' => [
                'subject' => 'Professional Networking',
                'body' => "Hello {employer_name},\n\nI came across your profile and was impressed by your work at {company}. I am currently exploring opportunities in {industry} and would appreciate any insights you might have about the industry.\n\nWould you be open to a brief coffee chat or phone call?\n\nThank you for considering my request.\n\nBest regards,\n{candidate_name}"
            ],
            'referral_request' => [
                'subject' => 'Referral Request',
                'body' => "Dear {employer_name},\n\nI hope you are doing well. I noticed that {company} has some exciting opportunities that align with my background and interests.\n\nWould you be willing to provide a referral or share any insights about the company culture and work environment?\n\nI would be happy to share my resume and discuss my qualifications.\n\nThank you for your consideration.\n\nBest regards,\n{candidate_name}"
            ]
        ];

        $generalMessagesCount = 0;
        $targetGeneralMessages = 30;

        for ($i = 0; $i < $targetGeneralMessages; $i++) {
            $candidate = $candidates->random();
            $employer = $employers->random();
            
            $messageType = fake()->randomElement(['company_inquiry', 'networking', 'referral_request']);
            $template = $generalTemplates[$messageType];
            
            Message::create([
                'sender_id' => $candidate->id,
                'recipient_id' => $employer->id,
                'subject' => $template['subject'],
                'body' => str_replace(
                    ['{candidate_name}', '{employer_name}', '{company}', '{field}', '{industry}'],
                    [
                        $candidate->name,
                        $employer->name,
                        $employer->company->name ?? 'your company',
                        fake()->randomElement(['software development', 'marketing', 'finance', 'design', 'sales']),
                        fake()->randomElement(['technology', 'finance', 'healthcare', 'education', 'retail'])
                    ],
                    $template['body']
                ),
                'read_at' => fake()->boolean(40) ? fake()->dateTimeBetween('-1 month', 'now') : null,
                'created_at' => fake()->dateTimeBetween('-2 months', 'now'),
            ]);
            
            $generalMessagesCount++;
            
            // 30% chance of getting a reply
            if (fake()->boolean(30)) {
                Message::create([
                    'sender_id' => $employer->id,
                    'recipient_id' => $candidate->id,
                    'subject' => 'Re: ' . $template['subject'],
                    'body' => "Dear {$candidate->name},\n\nThank you for reaching out. I appreciate your interest in our company.\n\nI would be happy to discuss potential opportunities. Please feel free to send me your resume and we can schedule a time to chat.\n\nBest regards,\n{$employer->name}",
                    'created_at' => fake()->dateTimeBetween('-1 month', 'now'),
                ]);
                $generalMessagesCount++;
            }
        }

        $this->command->info("Created {$generalMessagesCount} general messages");
    }
}
