@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">{{ $job->title }}</h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <a href="{{url('jobs')}}">Jobs</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>{{ $job->title }}</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                <div class="single-post d-flex flex-row">
                    <div class="thumb">
                        <img src="{{url('theme/web/img/post.png')}}" alt="">
                        <ul class="tags">
                            @foreach($job->categories as $category)
                                <li><a href="{{ route('category.jobs', $category->slug) }}">{{ $category->name }}</a></li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="details">
                        <div class="title d-flex flex-row justify-content-between">
                            <div class="titles">
                                <h2>{{ $job->title }}</h2>
                                <h6>{{ $job->company->name ?? 'Company' }}</h6>
                            </div>
                            <ul class="btns">
                                <li>
                                    <a href="#" onclick="toggleFavorite({{ $job->id }})"
                                       style="color: {{ isset($job->is_favorited) && $job->is_favorited ? '#ff0066' : '#6c757d' }}">
                                        <span class="lnr {{ isset($job->is_favorited) && $job->is_favorited ? 'lnr-heart' : 'lnr-heart' }}"></span>
                                    </a>
                                </li>
                                @auth
                                    @if(auth()->user()->hasRole('candidate'))
                                        @if($hasApplied)
                                            <li><span class="btn btn-success">Already Applied</span></li>
                                        @else
                                            <li><a href="#" onclick="showApplicationModal()" class="btn btn-primary">Apply Now</a></li>
                                        @endif
                                    @else
                                        <li><a href="{{ route('register') }}" class="btn btn-primary">Register to Apply</a></li>
                                    @endif
                                @else
                                    <li><a href="{{ route('login') }}" class="btn btn-primary">Login to Apply</a></li>
                                @endauth
                            </ul>
                        </div>
                        
                        <div class="mt-4">
                            <h4>Job Description</h4>
                            <p>{{ $job->description }}</p>
                        </div>
                        
                        @if($job->requirements)
                            <div class="mt-4">
                                <h4>Requirements</h4>
                                <p>{{ $job->requirements }}</p>
                            </div>
                        @endif
                        
                        @if($job->benefits)
                            <div class="mt-4">
                                <h4>Benefits</h4>
                                <p>{{ $job->benefits }}</p>
                            </div>
                        @endif
                        
                        <div class="mt-4">
                            <h4>Job Information</h4>
                            <h5>Job Nature: {{ ucfirst($job->job_type) }}</h5>
                            @if($job->experience_level)
                                <h5>Experience Level: {{ $job->experience_level }}</h5>
                            @endif
                            @if($job->education_level)
                                <h5>Education: {{ $job->education_level }}</h5>
                            @endif
                            <p class="address"><span class="lnr lnr-map"></span> {{ $job->location }}</p>
                            @if($job->salary_min && $job->salary_max)
                                <p class="address"><span class="lnr lnr-database"></span> ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}</p>
                            @endif
                            <p class="address"><span class="lnr lnr-calendar-full"></span> Posted {{ $job->created_at->diffForHumans() }}</p>
                            @if($job->deadline)
                                <p class="address"><span class="lnr lnr-alarm"></span> Deadline: {{ $job->deadline->format('M d, Y') }}</p>
                            @endif
                        </div>
                        
                        @if($job->company)
                            <div class="mt-4">
                                <h4>About {{ $job->company->name }}</h4>
                                @if($job->company->description)
                                    <p>{{ Str::limit($job->company->description, 300) }}</p>
                                @endif
                                <a href="{{ route('frontend.company', $job->company->slug) }}" class="btn btn-outline-primary">View Company Profile</a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4 sidebar">
                <div class="single-slidebar">
                    <h4>Quick Apply</h4>
                    <div class="mt-3">
                        @auth
                            @if(auth()->user()->hasRole('candidate'))
                                @if($hasApplied)
                                    <span class="btn btn-success btn-block mb-2">Already Applied</span>
                                @else
                                    <a href="#" onclick="showApplicationModal()" class="btn btn-primary btn-block mb-2">Apply Now</a>
                                @endif
                                <a href="#" onclick="toggleFavorite({{ $job->id }})"
                                   class="btn btn-outline-primary btn-block mb-2"
                                   style="color: {{ isset($job->is_favorited) && $job->is_favorited ? '#ff0066' : '' }}">
                                    {{ isset($job->is_favorited) && $job->is_favorited ? 'Remove from Favorites' : 'Save Job' }}
                                </a>
                            @else
                                <a href="{{ route('register') }}" class="btn btn-primary btn-block mb-2">Register to Apply</a>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class="btn btn-primary btn-block mb-2">Login to Apply</a>
                        @endauth
                        <a href="{{ route('frontend.company', $job->company->slug) }}" class="btn btn-outline-primary btn-block">View Company</a>
                    </div>
                </div>
                
                @if($relatedJobs->count() > 0)
                    <div class="single-slidebar">
                        <h4>Related Jobs</h4>
                        @foreach($relatedJobs as $relatedJob)
                            <div class="job-item mb-3">
                                <a href="{{ route('frontend.job.show', $relatedJob->slug) }}">
                                    <h6>{{ $relatedJob->title }}</h6>
                                </a>
                                <p>{{ $relatedJob->company->name ?? 'Company' }}</p>
                                <p><span class="lnr lnr-map"></span> {{ $relatedJob->location }}</p>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->

<!-- Job Application Modal -->
@auth
@if(auth()->user()->hasRole('candidate') && !$hasApplied)
<div class="modal fade" id="applicationModal" tabindex="-1" role="dialog" aria-labelledby="applicationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="applicationModalLabel">Apply for {{ $job->title }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="applicationForm" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="cover_letter">Cover Letter *</label>
                        <textarea class="form-control" id="cover_letter" name="cover_letter" rows="6"
                                  placeholder="Write your cover letter here (minimum 50 characters)..." required></textarea>
                        <small class="form-text text-muted">Minimum 50 characters required</small>
                    </div>

                    <div class="form-group">
                        <label for="resume">Resume *</label>
                        <input type="file" class="form-control-file" id="resume" name="resume"
                               accept=".pdf,.doc,.docx" required>
                        <small class="form-text text-muted">Upload your resume (PDF, DOC, DOCX - Max 5MB)</small>
                    </div>

                    <div class="form-group">
                        <label for="additional_documents">Additional Documents (Optional)</label>
                        <input type="file" class="form-control-file" id="additional_documents"
                               name="additional_documents[]" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                        <small class="form-text text-muted">Portfolio, certificates, etc. (Max 5MB each)</small>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                        <label class="form-check-label" for="terms">
                            I agree to the terms and conditions and privacy policy *
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Submit Application</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endauth

@endsection

@push('scripts')
<script>
// Show application modal
function showApplicationModal() {
    $('#applicationModal').modal('show');
}

// Handle application form submission
$('#applicationForm').on('submit', function(e) {
    e.preventDefault();

    var formData = new FormData(this);

    $.ajax({
        url: '{{ route("frontend.job.apply", $job->id) }}',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if(response.success) {
                $('#applicationModal').modal('hide');
                alert('Application submitted successfully!');
                location.reload(); // Refresh to show "Already Applied" status
            }
        },
        error: function(xhr) {
            if(xhr.status === 422) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'Please fix the following errors:\n';
                for(var field in errors) {
                    errorMessage += '- ' + errors[field][0] + '\n';
                }
                alert(errorMessage);
            } else {
                alert('An error occurred. Please try again.');
            }
        }
    });
});

// Favorite functionality
function toggleFavorite(jobId) {
    @auth
        $.ajax({
            url: '/jobs/' + jobId + '/favorite',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if(response.success) {
                    location.reload(); // Refresh to update favorite status
                }
            },
            error: function(xhr) {
                if(xhr.status === 401) {
                    alert('Please login to favorite jobs.');
                    window.location.href = '{{ route("login") }}';
                } else {
                    alert('Please try again later.');
                }
            }
        });
    @else
        alert('Please login to favorite jobs.');
        window.location.href = '{{ route("login") }}';
    @endauth
}
</script>
@endpush
