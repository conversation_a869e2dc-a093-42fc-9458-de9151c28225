@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">My Favorite Jobs</h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>My Favorites</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                @if($favorites->count() > 0)
                    @foreach($favorites as $job)
                        <div class="single-post d-flex flex-row">
                            <div class="thumb">
                                @if($job->company && $job->company->logo)
                                    <img src="{{ Storage::url($job->company->logo) }}" alt="{{ $job->company->name }}" style="width: 80px; height: 80px; object-fit: cover;">
                                @else
                                    <img src="{{url('theme/web/img/post.png')}}" alt="">
                                @endif
                                <ul class="tags">
                                    @foreach($job->categories as $category)
                                        <li><a href="{{ route('category.jobs', $category->slug) }}">{{ $category->name }}</a></li>
                                    @endforeach
                                </ul>
                            </div>
                            <div class="details">
                                <div class="title d-flex flex-row justify-content-between">
                                    <div class="titles">
                                        <a href="{{ route('frontend.job.show', $job->slug) }}">
                                            <h4>{{ $job->title }}</h4>
                                        </a>
                                        <h6>{{ $job->company->name ?? 'Company' }}</h6>
                                    </div>
                                    <ul class="btns">
                                        <li>
                                            <a href="#" onclick="removeFavorite({{ $job->id }})" style="color: #ff0066">
                                                <span class="lnr lnr-heart"></span>
                                            </a>
                                        </li>
                                        <li><a href="{{ route('frontend.job.show', $job->slug) }}" class="btn btn-primary">View Job</a></li>
                                    </ul>
                                </div>
                                <p>{{ Str::limit($job->description, 150) }}</p>
                                <h5>Job Nature: {{ ucfirst($job->job_type) }}</h5>
                                <p class="address"><span class="lnr lnr-map"></span> {{ $job->location }}</p>
                                @if($job->salary_min && $job->salary_max)
                                    <p class="address"><span class="lnr lnr-database"></span> ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}</p>
                                @endif
                                <p class="address"><span class="lnr lnr-calendar-full"></span> Posted {{ $job->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                    @endforeach
                    
                    <!-- Pagination -->
                    <div class="row">
                        <div class="col-lg-12">
                            {{ $favorites->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center">
                        <h4>No Favorite Jobs Yet</h4>
                        <p>You haven't added any jobs to your favorites yet. Browse jobs and click the heart icon to save them here.</p>
                        <a href="{{ route('jobs.all') }}" class="btn btn-primary">Browse Jobs</a>
                    </div>
                @endif
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4 sidebar">
                <div class="single-slidebar">
                    <h4>Quick Links</h4>
                    <ul class="cat-list">
                        <li><a href="{{ route('jobs.favorites') }}">My Favorites</a></li>
                        <li><a href="{{ route('jobs.shortlists') }}">My Shortlists</a></li>
                        <li><a href="{{ route('jobs.all') }}">Browse All Jobs</a></li>
                        <li><a href="{{ route('jobs.search') }}">Search Jobs</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->
@endsection

@push('scripts')
<script>
function removeFavorite(jobId) {
    if(confirm('Are you sure you want to remove this job from your favorites?')) {
        $.ajax({
            url: '/jobs/' + jobId + '/favorite',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if(response.success) {
                    location.reload();
                }
            },
            error: function(xhr) {
                alert('An error occurred. Please try again.');
            }
        });
    }
}
</script>
@endpush
