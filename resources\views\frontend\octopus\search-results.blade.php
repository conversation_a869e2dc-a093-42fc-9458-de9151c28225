@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">Search Results</h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>Search Results</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                @if(isset($query))
                    <div class="mb-4 text-center">
                        <h4>Search results for: "{{ $query }}"</h4>
                        <p>Found {{ $jobs->total() }} {{ Str::plural('job', $jobs->total()) }}</p>
                    </div>
                @endif
                
                @if($jobs->count() > 0)
                    @foreach($jobs as $job)
                        <div class="single-post d-flex flex-row">
                            <div class="thumb">
                                <img src="{{url('theme/web/img/post.png')}}" alt="">
                                <ul class="tags">
                                    @foreach($job->categories as $category)
                                        <li><a href="{{ route('category.jobs', $category->slug) }}">{{ $category->name }}</a></li>
                                    @endforeach
                                </ul>
                            </div>
                            <div class="details">
                                <div class="title d-flex flex-row justify-content-between">
                                    <div class="titles">
                                        <a href="{{ route('frontend.job.show', $job->slug) }}">
                                            <h4>{{ $job->title }}</h4>
                                        </a>
                                        <h6>{{ $job->company->name ?? 'Company' }}</h6>
                                    </div>
                                    <ul class="btns">
                                        @auth
                                            <li>
                                                <a href="#" onclick="toggleFavorite({{ $job->id }})"
                                                   class="{{ isset($job->is_favorited) && $job->is_favorited ? 'text-danger' : '' }}">
                                                    <span class="lnr lnr-heart"></span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#" onclick="toggleShortlist({{ $job->id }})"
                                                   class="{{ isset($job->is_shortlisted) && $job->is_shortlisted ? 'text-primary' : '' }}">
                                                    <span class="lnr lnr-bookmark"></span>
                                                </a>
                                            </li>
                                        @endauth
                                        <li><a href="{{ route('frontend.job.show', $job->slug) }}">Apply</a></li>
                                    </ul>
                                </div>
                                <p>{{ Str::limit($job->description, 200) }}</p>
                                <h5>Job Nature: {{ ucfirst($job->job_type) }}</h5>
                                <p class="address"><span class="lnr lnr-map"></span> {{ $job->location }}</p>
                                @if($job->salary_min && $job->salary_max)
                                    <p class="address"><span class="lnr lnr-database"></span> ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}</p>
                                @endif
                            </div>
                        </div>
                    @endforeach
                    
                    <!-- Pagination -->
                    <div class="row justify-content-center">
                        <div class="col-lg-12">
                            {{ $jobs->appends(request()->query())->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center">
                        <h4>No jobs found</h4>
                        @if(isset($query))
                            <p>No jobs found for "{{ $query }}". Try different keywords or browse all jobs.</p>
                        @else
                            <p>No jobs found matching your criteria.</p>
                        @endif
                        <a href="{{ route('frontend.jobs') }}" class="btn btn-primary">Browse All Jobs</a>
                    </div>
                @endif
            </div>
            
            <!-- Search Sidebar -->
            <div class="col-lg-4 sidebar">
                <div class="single-slidebar">
                    <h4>Refine Search</h4>
                    <form action="{{ route('jobs.search') }}" method="GET">
                        <div class="form-group">
                            <label>Keywords</label>
                            <input type="text" name="keywords" class="form-control" value="{{ $searchParams['keywords'] ?? '' }}" placeholder="Job title, skills, company">
                        </div>
                        <div class="form-group">
                            <label>Location</label>
                            <input type="text" name="location" class="form-control" value="{{ $searchParams['location'] ?? '' }}" placeholder="City, state, country">
                        </div>
                        <div class="form-group">
                            <label>Category</label>
                            <select name="category" class="form-control">
                                <option value="">All Categories</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ ($searchParams['category'] ?? '') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Job Type</label>
                            <select name="job_type" class="form-control">
                                <option value="">All Types</option>
                                <option value="full_time" {{ ($searchParams['job_type'] ?? '') == 'full_time' ? 'selected' : '' }}>Full Time</option>
                                <option value="part_time" {{ ($searchParams['job_type'] ?? '') == 'part_time' ? 'selected' : '' }}>Part Time</option>
                                <option value="contract" {{ ($searchParams['job_type'] ?? '') == 'contract' ? 'selected' : '' }}>Contract</option>
                                <option value="freelance" {{ ($searchParams['job_type'] ?? '') == 'freelance' ? 'selected' : '' }}>Freelance</option>
                                <option value="internship" {{ ($searchParams['job_type'] ?? '') == 'internship' ? 'selected' : '' }}>Internship</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Experience Level</label>
                            <select name="experience_level" class="form-control">
                                <option value="">Any Level</option>
                                <option value="entry" {{ ($searchParams['experience_level'] ?? '') == 'entry' ? 'selected' : '' }}>Entry Level</option>
                                <option value="mid" {{ ($searchParams['experience_level'] ?? '') == 'mid' ? 'selected' : '' }}>Mid Level</option>
                                <option value="senior" {{ ($searchParams['experience_level'] ?? '') == 'senior' ? 'selected' : '' }}>Senior Level</option>
                                <option value="executive" {{ ($searchParams['experience_level'] ?? '') == 'executive' ? 'selected' : '' }}>Executive</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary btn-block">Search Jobs</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->

@auth
<script>
function toggleFavorite(jobId) {
    fetch(`/jobs/${jobId}/favorite`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'Error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error occurred while updating favorites');
    });
}

function toggleShortlist(jobId) {
    fetch(`/jobs/${jobId}/shortlist`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'Error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error occurred while updating shortlist');
    });
}
</script>
@endauth

@endsection
