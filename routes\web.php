<?php

use App\Http\Controllers\JobApplicationController;
use App\Http\Controllers\CandidateController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\JobListingController;
use App\Http\Controllers\MessageController;

use App\Http\Controllers\UserController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\BusinessSettingController;
use App\Http\Controllers\WebController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\SitemapController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use Illuminate\Support\Facades\Route;

// Homepage
Route::get('/', [WebController::class, 'index'])->name('home');

// Job Categories - Dynamic routes
Route::get('/category/{slug}', [WebController::class, 'categoryJobs'])->name('category.jobs');

// Job Types - Dynamic routes
Route::get('/volunteering', [WebController::class, 'jobsByType'])->defaults('type', 'volunteering')->name('jobs.volunteering');
Route::get('/casual', [WebController::class, 'jobsByType'])->defaults('type', 'casual')->name('jobs.casual');
Route::get('/internship', [WebController::class, 'jobsByType'])->defaults('type', 'internship')->name('jobs.internship');
Route::get('/business', [WebController::class, 'jobsByType'])->defaults('type', 'business')->name('jobs.business');
Route::get('/franchise', [WebController::class, 'jobsByType'])->defaults('type', 'franchise')->name('jobs.franchise');
Route::get('/part_time', [WebController::class, 'jobsByType'])->defaults('type', 'part_time')->name('jobs.part_time');

// Job Search with rate limiting
Route::get('/search', [WebController::class, 'searchJobs'])
    ->middleware('throttle:60,1')
    ->name('jobs.search');
Route::post('/search', [WebController::class, 'searchJobs'])
    ->middleware('throttle:60,1')
    ->name('jobs.search.post');
Route::get('/jobs', [WebController::class, 'allJobs'])->name('jobs.all');
Route::get('/job/{slug}', [WebController::class, 'jobDetail'])->name('frontend.job.show');
Route::post('/job/{jobId}/apply', [WebController::class, 'applyForJob'])->middleware('auth')->name('frontend.job.apply');

// Companies (public page)
Route::get('/companies', [WebController::class, 'companies'])->name('frontend.companies');
Route::get('/company/{slug}', [WebController::class, 'companyProfile'])->name('frontend.company');

Route::get('/company/{slug}/jobs', [WebController::class, 'companyJobs'])->name('frontend.company.jobs');

// SEO Routes
Route::get('/sitemap.xml', [SitemapController::class, 'index'])->name('sitemap');
Route::get('/robots.txt', [SitemapController::class, 'robots'])->name('robots');


// Dashboard route using AdminDashboardController@index
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'role:superadmin|employer|candidate'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Job Interactions (AJAX endpoints)
    Route::post('/jobs/{jobId}/favorite', [App\Http\Controllers\JobInteractionController::class, 'toggleFavorite'])
        ->middleware('throttle:60,1')
        ->name('jobs.favorite');
    Route::post('/jobs/{jobId}/shortlist', [App\Http\Controllers\JobInteractionController::class, 'toggleShortlist'])
        ->middleware('throttle:60,1')
        ->name('jobs.shortlist');
    Route::get('/my-favorites', [App\Http\Controllers\JobInteractionController::class, 'showFavorites'])
        ->name('jobs.favorites');
    Route::get('/my-shortlists', [App\Http\Controllers\JobInteractionController::class, 'showShortlists'])
        ->name('jobs.shortlists');

    // Jobs
    Route::resource('jobs-listing', JobListingController::class);

    // Applications with file upload security
    Route::resource('applications', JobApplicationController::class)
        ->middleware('secure.upload');
    Route::get('my-applications', [JobApplicationController::class, 'myApplications'])->name('applications.my-applications');
    Route::post('applications/{application}/status', [JobApplicationController::class, 'updateStatus'])
        ->middleware('rate.limit:status,10,1') // 10 status updates per minute
        ->name('applications.update-status');
    Route::get('applications/{application}/document/{type}/{index?}', [JobApplicationController::class, 'downloadDocument'])->name('applications.document');

    // Companies
    Route::resource('companies-listing', CompanyController::class);

    // Candidates
    Route::get('candidates', [CandidateController::class, 'index'])->name('candidates.index');
    Route::get('candidates/{candidate}', [CandidateController::class, 'show'])->name('candidates.show');
    Route::get('candidates/profile/{profile}/edit', [CandidateController::class, 'edit'])->name('candidates.edit');
    Route::put('candidates/profile/{profile}', [CandidateController::class, 'update'])->name('candidates.update');
    Route::get('candidates/profile/{profile}/resume', [CandidateController::class, 'downloadResume'])->name('candidates.resume');

    // Messages
    Route::resource('messages', MessageController::class);

    // Categories (Super Admin only)
    Route::resource('categories', CategoryController::class)->middleware('role:superadmin');

    // Users (Super Admin only)
    Route::resource('users', UserController::class)->middleware('role:superadmin');

    // Business Settings - Only accessible to users with settings permission
    Route::middleware(['auth', 'role:superadmin|admin'])->controller(BusinessSettingController::class)->group(function () {
        Route::post('/business-settings/update', 'update')->name('business_settings.update');
        Route::post('/business-settings/update/activation', 'updateActivationSettings')->name('business_settings.update.activation');
        Route::post('/payment-activation', 'updatePaymentActivationSettings')->name('payment.activation');
        Route::get('/general-setting', 'general_setting')->name('general_setting.index');
        Route::get('/activation', 'activation')->name('activation.index');
        Route::get('/payment-method', 'payment_method')->name('payment_method.index');
        Route::get('/file_system', 'file_system')->name('file_system.index');
        Route::get('/social-login', 'social_login')->name('social_login.index');
        Route::post('/social-login-update', 'social_login_update')->name('social_login_update');
        Route::get('/smtp-settings', 'smtp_settings')->name('smtp_settings.index');
        Route::get('/google-analytics', 'google_analytics')->name('google_analytics.index');
        Route::get('/google-recaptcha', 'google_recaptcha')->name('google_recaptcha.index');
        Route::get('/google-map', 'google_map')->name('google-map.index');
        Route::get('/google-firebase', 'google_firebase')->name('google-firebase.index');

        // Theme management routes
        Route::get('/theme-settings', 'theme_settings')->name('theme_settings');
        Route::post('/update-theme', 'update_theme')->name('update_theme');

        //Facebook Settings
        Route::get('/facebook-chat', 'facebook_chat')->name('facebook_chat.index');
        Route::post('/facebook_chat', 'facebook_chat_update')->name('facebook_chat.update');
        Route::get('/facebook-comment', 'facebook_comment')->name('facebook-comment');
        Route::post('/facebook-comment', 'facebook_comment_update')->name('facebook-comment.update');
        Route::post('/facebook_pixel', 'facebook_pixel_update')->name('facebook_pixel.update');

        Route::post('/env_key_update', 'env_key_update')->name('env_key_update.update');
        Route::post('/payment_method_update', 'payment_method_update')->name('payment_method.update');
        Route::post('/google_analytics', 'google_analytics_update')->name('google_analytics.update');
        Route::post('/google_recaptcha', 'google_recaptcha_update')->name('google_recaptcha.update');
        Route::post('/google-map', 'google_map_update')->name('google-map.update');
        Route::post('/google-firebase', 'google_firebase_update')->name('google-firebase.update');

        Route::get('/verification/form', 'seller_verification_form')->name('seller_verification_form.index');
        Route::post('/verification/form', 'seller_verification_form_update')->name('seller_verification_form.update');
        Route::get('/vendor_commission', 'vendor_commission')->name('business_settings.vendor_commission');

        //Shipping Configuration
        Route::get('/shipping_configuration', 'shipping_configuration')->name('shipping_configuration.index');
        Route::post('/shipping_configuration/update', 'shipping_configuration_update')->name('shipping_configuration.update');

        // Order Configuration
        Route::get('/order-configuration', 'order_configuration')->name('order_configuration.index');
        Route::post('/tailwind-config-update', [BusinessSettingController::class, 'tailwind_config_update'])->name('business_settings.tailwind_config_update');

        // Test Email Route
        Route::post('/business-settings/test-email', [BusinessSettingController::class, 'test_email'])->name('business_settings.test_email');

    });

});

require __DIR__.'/auth.php';

// Dynamic pages - Handle all octopus theme pages dynamically
Route::get('/{slug}', [WebController::class, 'dynamicPage'])
    ->name('dynamic.page')
    ->where('slug', '[a-zA-Z0-9\-_]+');
